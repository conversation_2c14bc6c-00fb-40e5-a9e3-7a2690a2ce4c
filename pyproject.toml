[project]
name = "nfl-elite-projections"
version = "0.1.0"
requires-python = ">=3.10"
dependencies = [
  "pandas>=2.2",
  "numpy>=1.26",
  "pyarrow>=16.0",
  "requests>=2.32",
  "httpx>=0.27",
  "tenacity>=8.3",
  "orjson>=3.10",
  "python-dotenv>=1.0",
  "pydantic>=2.8",
  "rapidfuzz>=3.9",
  "tqdm>=4.66",
  "rich>=13.7",
  "scipy>=1.13"
]

[tool.ruff]
line-length = 100


[build-system]
requires = ["setuptools>=68", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]
