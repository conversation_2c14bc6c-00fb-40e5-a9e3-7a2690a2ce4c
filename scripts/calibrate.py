#!/usr/bin/env python3
from __future__ import annotations

import argparse
import os

import pandas as pd

from src.model.calibration import load_hist_bins, calibrate_means, calibrate_tails


def main() -> None:
    parser = argparse.ArgumentParser(description="Calibrate simulated projection summary and save outputs.")
    parser.add_argument("--input", default="/data/outputs/sim_summary.parquet", help="Path to sim summary parquet/csv")
    parser.add_argument("--output", default="/data/outputs/projections_calibrated.parquet", help="Output path")
    args = parser.parse_args()

    # Load sim summary
    inp = args.input
    if not os.path.isabs(inp):
        inp = os.path.abspath(os.path.join(os.getcwd(), inp))
    if inp.endswith(".csv"):
        df = pd.read_csv(inp)
    else:
        try:
            df = pd.read_parquet(inp)
        except Exception:
            df = pd.read_csv(inp)

    hist = load_hist_bins()

    df1 = calibrate_means(df, hist)
    df2 = calibrate_tails(df1, hist)

    out = args.output
    if not os.path.isabs(out):
        out = os.path.abspath(os.path.join(os.getcwd(), out))
    os.makedirs(os.path.dirname(out), exist_ok=True)

    try:
        df2.to_parquet(out, index=False)
    except Exception:
        df2.to_csv(out.replace(".parquet", ".csv"), index=False)

    print(f"Calibrated projections saved to: {out}")


if __name__ == "__main__":
    main()

