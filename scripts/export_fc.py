#!/usr/bin/env python3
from __future__ import annotations

import argparse
import glob
import os
from pathlib import Path

import pandas as pd

from src.export.fc_export import to_fantasy_cruncher


def _find_first(patterns):
    for pat in patterns:
        files = sorted(glob.glob(pat))
        if files:
            return files[0]
    return None


def main() -> None:
    parser = argparse.ArgumentParser(description="Export calibrated projections to Fantasy Cruncher format.")
    parser.add_argument("--date", required=True, help="Slate date YYYY-MM-DD")
    parser.add_argument("--site", default="dk", choices=["dk", "fd"], help="Site key")
    parser.add_argument("--league", default="NFL")
    parser.add_argument("--out", default=None, help="Output CSV path (optional)")
    args = parser.parse_args()

    root = os.path.abspath(os.getcwd())

    # Load features/salaries
    features_path = _find_first([
        os.path.join(root, f"data/cache/features_{args.league}_{args.date}.parquet"),
        os.path.join(root, "data/cache/features.parquet"),
    ])
    if features_path and features_path.endswith(".parquet"):
        features = pd.read_parquet(features_path)
    elif features_path:
        features = pd.read_csv(features_path)
    else:
        features = pd.DataFrame()

    # Salaries
    if args.site == "dk":
        sal_path = _find_first([
            os.path.join(root, f"data/cache/dk_salaries_{args.date}_*.parquet"),
            os.path.join(root, f"data/dk_salaries_{args.date}_*.csv"),
        ])
    else:
        sal_path = _find_first([
            os.path.join(root, f"data/cache/fd_salaries_{args.date}_*.parquet"),
            os.path.join(root, f"data/fd_salaries_{args.date}_*.csv"),
        ])

    if sal_path is None:
        raise FileNotFoundError("Salaries file not found in /data/cache or /data for given date/site")

    salaries = pd.read_parquet(sal_path) if sal_path.endswith(".parquet") else pd.read_csv(sal_path)

    # Calibrated projections
    proj_path = _find_first([
        os.path.join(root, "data/outputs/projections_calibrated.parquet"),
        os.path.join(root, "data/outputs/projections_calibrated.csv"),
    ])
    if proj_path is None:
        raise FileNotFoundError("Calibrated projections not found at /data/outputs")

    df_cal = pd.read_parquet(proj_path) if proj_path.endswith(".parquet") else pd.read_csv(proj_path)

    fc = to_fantasy_cruncher(df_cal, salaries, None)

    out_dir = os.path.join(root, "data/exports")
    os.makedirs(out_dir, exist_ok=True)
    out_path = args.out or os.path.join(out_dir, f"fc_{args.site}_{args.date}.csv")
    fc.to_csv(out_path, index=False)

    print(f"FC export saved to: {out_path}")


if __name__ == "__main__":
    main()

