#!/usr/bin/env python3
from __future__ import annotations

import argparse
from pathlib import Path

import pandas as pd

from src.pipeline.io import save_table
from src.pipeline.join_table import build_feature_table


def main() -> None:
    parser = argparse.ArgumentParser(description="Build joined feature table and save to cache.")
    parser.add_argument("--date", required=True, help="Slate date in YYYY-MM-DD")
    parser.add_argument("--league", default="NFL", help="League code (default: NFL)")
    args = parser.parse_args()

    df = build_feature_table(args.date, args.league)

    name = f"features_{args.league}_{args.date}"
    out_path = save_table(df, name)

    print(f"Saved feature table: {out_path}")
    print(f"Rows: {len(df):,}  Cols: {len(df.columns):,}")


if __name__ == "__main__":
    main()

