from __future__ import annotations

import sys
from pathlib import Path

import pandas as pd
import numpy as np

# Ensure src/ is on path for direct package import
ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(ROOT / "src"))

from projections.baseline import compute_baselines  # noqa: E402


def _make_qb_df(pass_rate: float) -> pd.DataFrame:
    return pd.DataFrame([
        {
            "player_id": "qb1",
            "slate_id": "sl1",
            "team": "AAA",
            "opp": "BBB",
            "pos": "QB",
            "team_plays": 60,
            "pass_rate": pass_rate,
            "yards_per_att": 7.5,
            "pass_td_rate": 0.05,
            "rush_share_qb": 0.1,
            "yards_per_carry": 5.0,
            "rush_td_rate_qb": 0.03,
            "snap_share": 0.98,
            "implied_total": 24,
        }
    ])


def _make_rb_df(rush_share: float) -> pd.DataFrame:
    return pd.DataFrame([
        {
            "player_id": "rb1",
            "slate_id": "sl1",
            "team": "AAA",
            "opp": "BBB",
            "pos": "RB",
            "team_plays": 62,
            "pass_rate": 0.52,
            "rush_share": rush_share,
            "target_share": 0.10,
            "yards_per_carry": 4.6,
            "yards_per_tgt": 6.5,
            "catch_rate": 0.75,
            "rush_td_rate": 0.03,
            "rec_td_rate": 0.02,
            "rz_rush_share": 0.45,
            "rz_tgt_share": 0.07,
            "snap_share": 0.70,
            "implied_total": 23,
        }
    ])


def _make_wr_df(target_share: float) -> pd.DataFrame:
    return pd.DataFrame([
        {
            "player_id": "wr1",
            "slate_id": "sl1",
            "team": "AAA",
            "opp": "BBB",
            "pos": "WR",
            "team_plays": 64,
            "pass_rate": 0.60,
            "target_share": target_share,
            "yards_per_tgt": 8.8,
            "catch_rate": 0.62,
            "rec_td_rate": 0.05,
            "aDOT": 11.0,
            "snap_share": 0.85,
            "implied_total": 25,
        }
    ])


def test_qb_scales_with_pass_rate():
    low = compute_baselines(_make_qb_df(0.50), {})
    high = compute_baselines(_make_qb_df(0.65), {})
    assert high.loc[0, "baseline_dk"] > low.loc[0, "baseline_dk"]


def test_rb_scales_with_rush_share():
    low = compute_baselines(_make_rb_df(0.40), {})
    high = compute_baselines(_make_rb_df(0.70), {})
    assert high.loc[0, "baseline_dk"] > low.loc[0, "baseline_dk"]


def test_wr_scales_with_target_share():
    low = compute_baselines(_make_wr_df(0.18), {})
    high = compute_baselines(_make_wr_df(0.28), {})
    assert high.loc[0, "baseline_dk"] > low.loc[0, "baseline_dk"]


def test_handles_missing_values_conservatively():
    # Minimal row missing many fields
    df = pd.DataFrame([
        {"player_id": "x", "slate_id": "sl", "team": "AAA", "opp": "BBB", "pos": "TE"}
    ])
    out = compute_baselines(df, {})
    assert np.isfinite(out.loc[0, "baseline_dk"]) and out.loc[0, "baseline_dk"] >= 0.0
    assert out.loc[0, "minutes_or_snaps"] > 0


def test_output_keys_present():
    df = _make_qb_df(0.58)
    out = compute_baselines(df, {})
    row = out.loc[0]
    for k in [
        "player_id",
        "slate_id",
        "team",
        "opp",
        "pos",
        "baseline_dk",
        "minutes_or_snaps",
        "volatility_prior",
        "metadata",
    ]:
        assert k in out.columns
        assert row[k] is not None

