from __future__ import annotations

import sys
from pathlib import Path

import numpy as np
import pandas as pd

ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(ROOT / "src"))

from sims.monte_carlo import simulate_slate  # noqa: E402


def _toy_team():
    # Simple slate: QB+WR vs opposing QB+WR
    return pd.DataFrame([
        {"player_id": "QB_A", "team": "AAA", "opp": "BBB", "pos": "QB", "baseline_dk": 20.0, "volatility_prior": 5.0, "salary": 7000},
        {"player_id": "WR_A1", "team": "AAA", "opp": "BBB", "pos": "WR", "baseline_dk": 15.0, "volatility_prior": 5.0, "target_share": 0.25, "salary": 6500},
        {"player_id": "QB_B", "team": "BBB", "opp": "AAA", "pos": "QB", "baseline_dk": 19.0, "volatility_prior": 5.0, "salary": 6800},
        {"player_id": "WR_B1", "team": "BBB", "opp": "AAA", "pos": "WR", "baseline_dk": 14.0, "volatility_prior": 5.0, "target_share": 0.24, "salary": 6000},
    ])


def test_qb_mean_near_baseline():
    df = _toy_team()
    out = simulate_slate(df, n=4000, seed=1, mode="independent")
    qb_row = out[out.player_id == "QB_A"].iloc[0]
    assert abs(qb_row["mean"] - 20.0) < 0.5


def test_wr_tail_increases_with_stack_corr():
    df = _toy_team()
    out_ind = simulate_slate(df, n=6000, seed=2, mode="independent")
    out_corr = simulate_slate(df, n=6000, seed=2, mode="fast")
    wr_ind = float(out_ind[out_ind.player_id == "WR_A1"]["p90"].iloc[0])
    wr_corr = float(out_corr[out_corr.player_id == "WR_A1"]["p90"].iloc[0])
    # With positive QB-WR corr, WR tail should be higher or equal on average
    assert wr_corr >= wr_ind - 0.2

