from __future__ import annotations

import json
import os
from typing import Dict

import numpy as np
import pandas as pd


def load_hist_bins() -> Dict:
    """Load historical calibration bins from /data/hist.

    Expected file: /data/hist/priors.json with structure like:
    {
      "pos_prior_mean": {"QB": 15.0, "RB": 10.0, ...},
      "shrinkage_k": {"QB": 800.0, "RB": 600.0, ...},
      "tail_scale": {"QB": 1.0, "RB": 1.0, ...}
    }
    Returns sensible defaults if missing.
    """
    root = os.path.abspath(os.path.join(os.getcwd(), "data", "hist"))
    path = os.path.join(root, "priors.json")
    if os.path.exists(path):
        with open(path, "r") as f:
            return json.load(f)
    # Defaults
    return {
        "pos_prior_mean": {"QB": 16.0, "RB": 12.0, "WR": 12.0, "TE": 9.0, "DST": 7.0, "K": 8.0},
        "shrinkage_k": {"QB": 800.0, "RB": 600.0, "WR": 600.0, "TE": 500.0, "DST": 300.0, "K": 300.0},
        "tail_scale": {"QB": 1.0, "RB": 1.0, "WR": 1.0, "TE": 1.0, "DST": 1.0, "K": 1.0},
    }


def calibrate_means(df_summary: pd.DataFrame, hist_bins: Dict) -> pd.DataFrame:
    df = df_summary.copy()
    pos_prior = hist_bins.get("pos_prior_mean", {})
    shrink_k = hist_bins.get("shrinkage_k", {})

    # Weight based on samples and noisiness; use sim_samples_used and std
    n = df.get("sim_samples_used", pd.Series([10000] * len(df))).astype(float)
    std = df.get("std", pd.Series([5.0] * len(df))).astype(float)

    w = n / (n + pd.Series([shrink_k.get(p, 500.0) for p in df["pos"].astype(str).str.upper()]))
    prior_vec = pd.Series([pos_prior.get(p, df.loc[i, "mean"]) for i, p in enumerate(df["pos"].astype(str).str.upper())])

    df["Projection"] = w * df["mean"].astype(float) + (1.0 - w) * prior_vec.astype(float)
    return df


def calibrate_tails(df_summary: pd.DataFrame, hist_bins: Dict) -> pd.DataFrame:
    df = df_summary.copy()
    tail_scale = hist_bins.get("tail_scale", {})

    # Simple monotonic tail calibration: scale distance from mean by a pos-specific factor
    scale_vec = pd.Series([tail_scale.get(p, 1.0) for p in df["pos"].astype(str).str.upper()]).astype(float)

    # Adjust p90 (and recompute p75 proportionally)
    mean = df.get("mean", pd.Series([0.0] * len(df))).astype(float)
    std = df.get("std", pd.Series([5.0] * len(df))).astype(float)

    # Raw distances
    d90 = df.get("p90", mean + 1.28 * std) - mean
    d75 = df.get("p75", mean + 0.67 * std) - mean

    df["Ceiling"] = mean + scale_vec * d90
    df["P75"] = mean + scale_vec * d75

    # Optionally set Floor (approx p15) as calibrated too
    df["Floor"] = mean - 1.036 * std  # keep simple; could also scale

    return df

