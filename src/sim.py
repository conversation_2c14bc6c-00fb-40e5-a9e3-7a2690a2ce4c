from __future__ import annotations

from typing import Optional, Tuple

import numpy as np
import pandas as pd

# DK scoring constants
_DK_PASS_YARD_PTS = 0.04
_DK_RUSH_REC_YARD_PTS = 0.1
_DK_PASS_TD_PTS = 4.0
_DK_RUSH_REC_TD_PTS = 6.0
_DK_REC_PTS = 1.0


def _series_num(s: pd.Series, default: float) -> pd.Series:
    return pd.to_numeric(s, errors="coerce").fillna(default).astype(float)


def _get(df: pd.DataFrame, col: str, default: float) -> pd.Series:
    return _series_num(df.get(col, pd.Series([default] * len(df), index=df.index)), default)


def _pos_of(row: pd.Series) -> str:
    # Accept either DK's Position/Roster Position columns or pos
    for c in ("Position", "Roster Position", "pos", "Pos"):
        if c in row.index and isinstance(row[c], str) and row[c]:
            # DK may have multi-pos like "WR/TE"; take first
            return row[c].split('/')[0].upper()
    return ""


def _team_of(row: pd.Series) -> str:
    for c in ("Team", "TeamAbbrev", "team"):
        if c in row.index and isinstance(row[c], str) and row[c]:
            return row[c].upper()
    return ""


def _vegas_team_avg(df: pd.DataFrame) -> float:
    if "Team" in df.columns:
        g = df.groupby("Team")["VegasPts"].mean(numeric_only=True)
        if not g.empty:
            return float(g.mean())
    v = pd.to_numeric(df.get("VegasPts"), errors="coerce")
    m = float(v.mean(skipna=True)) if not v.isna().all() else 21.0
    return m if m > 0 else 21.0


def simulate_players(df: pd.DataFrame, n_sims: int = 10000, random_state: Optional[int] = None) -> pd.DataFrame:
    """Monte Carlo simulate DK points for each player and return distribution stats.

    Inputs expected on df (best-effort, sensible defaults if missing):
      - VegasPts (team implied points)
      - Usage: targets, rush_att, snap_pct, routes (optional)
      - Efficiency: yards_per_tgt, yards_per_carry, catch_rate, rec_td_rate, rush_td_rate, pass_td_rate
      - Defense adjustments if present: dvoa_vs_* (multipliers), rz_td_allowed (rate add)

    Outputs per player index: mean, p20, p80, p90
    """
    rng = np.random.default_rng(random_state)

    vegas = _get(df, "VegasPts", 21.0)
    league_avg = _vegas_team_avg(df)
    vegas_scale = (vegas / league_avg).clip(0.5, 1.5)

    # Usage priors
    targets = _get(df, "targets", 0.0)
    rush_att = _get(df, "rush_att", 0.0)
    snap_pct = _get(df, "snap_pct", 0.7)

    # Efficiency priors
    ypt = _get(df, "yards_per_tgt", 7.5)
    ypc = _get(df, "yards_per_carry", 4.4)
    catch_rate = _get(df, "catch_rate", 0.65).clip(0.3, 0.9)
    rec_td_rate = _get(df, "rec_td_rate", 0.04).clip(0.0, 0.12)
    rush_td_rate = _get(df, "rush_td_rate", 0.03).clip(0.0, 0.12)
    pass_td_rate = _get(df, "pass_td_rate", 0.045).clip(0.0, 0.12)

    # Defensive adjustments (neutral=1.0 multipliers / 0.0 rates)
    d_qb = _get(df, "dvoa_vs_qb", 1.0)
    d_rb = _get(df, "dvoa_vs_rb", 1.0)
    d_wr = _get(df, "dvoa_vs_wr", 1.0)
    d_te = _get(df, "dvoa_vs_te", 1.0)
    rz_td_allowed = _get(df, "rz_td_allowed", 0.0)

    # Precompute for speed
    n = len(df)
    means = np.zeros(n, dtype=float)
    p20s = np.zeros(n, dtype=float)
    p80s = np.zeros(n, dtype=float)
    p90s = np.zeros(n, dtype=float)

    # Loop rows (vectorizing across sims inside)
    for i, (_, row) in enumerate(df.iterrows()):
        pos = _pos_of(row)
        team_scale = float(vegas_scale.iat[i])

        # Row-specific params
        r_targets = float(targets.iat[i])
        r_rush_att = float(rush_att.iat[i])
        r_ypt = float(ypt.iat[i])
        r_ypc = float(ypc.iat[i])
        r_cr = float(catch_rate.iat[i])
        r_rec_td = float(rec_td_rate.iat[i])
        r_rush_td = float(rush_td_rate.iat[i])
        r_pass_td = float(pass_td_rate.iat[i])
        r_rz_add = float(rz_td_allowed.iat[i])

        # Sensible position-based defaults when usage is missing
        if r_targets < 0.5:
            if pos == "RB":
                r_targets = 3.0
            elif pos == "WR":
                r_targets = 6.0
            elif pos == "TE":
                r_targets = 5.0
            elif pos == "QB":
                r_targets = 28.0
        if r_rush_att < 0.5:
            if pos == "RB":
                r_rush_att = 12.0
            elif pos == "QB":
                r_rush_att = 4.0
            else:
                r_rush_att = 0.3

        # Scale volume by snap percentage and team context
        r_targets *= float(snap_pct.iat[i]) * (0.6 + 0.4 * team_scale)
        r_rush_att *= float(snap_pct.iat[i]) * (0.6 + 0.4 * team_scale)

        # Defensive multipliers by position
        if pos == "QB":
            d_mult = float(d_qb.iat[i])
        elif pos == "RB":
            d_mult = float(d_rb.iat[i])
        elif pos == "TE":
            d_mult = float(d_te.iat[i])
        else:  # WR and others
            d_mult = float(d_wr.iat[i])

        # Scale efficiencies by defense and vegas
        eff_ypt = max(2.0, r_ypt * d_mult) * team_scale
        eff_ypc = max(2.0, r_ypc * d_mult) * team_scale
        eff_rec_td = max(0.0, min(0.20, r_rec_td * d_mult + 0.2 * r_rz_add))
        eff_rush_td = max(0.0, min(0.20, r_rush_td * d_mult + 0.2 * r_rz_add))

        # Volume randomization (lognormal-ish by sampling normal then clipping)
        # Targets/carries may be zero for many positions; add small jitter
        tgt = np.clip(rng.normal(loc=r_targets, scale=max(1.0, 0.35 * max(1.0, r_targets)), size=n_sims), 0.0, None)
        car = np.clip(rng.normal(loc=r_rush_att, scale=max(1.0, 0.35 * max(1.0, r_rush_att)), size=n_sims), 0.0, None)

        # Receptions and yards
        rec = rng.binomial(n=np.maximum(0, tgt).astype(int), p=min(0.9, max(0.05, r_cr)))
        rec_yards = rng.normal(loc=rec * eff_ypt, scale=np.sqrt(np.maximum(1.0, rec)) * (eff_ypt * 0.35))
        rush_yards = rng.normal(loc=car * eff_ypc, scale=np.sqrt(np.maximum(1.0, car)) * (eff_ypc * 0.35))
        rec_yards = np.clip(rec_yards, 0.0, None)
        rush_yards = np.clip(rush_yards, 0.0, None)

        # Touchdowns (binomial approximations)
        rec_tds = rng.binomial(n=np.maximum(0, tgt).astype(int), p=eff_rec_td)
        rush_tds = rng.binomial(n=np.maximum(0, car).astype(int), p=eff_rush_td)

        # QB: add passive passing component if QB
        pass_pts = 0.0
        if pos == "QB":
            # Approximate attempts from targets + scaling; if missing, base on vegas_scale
            base_att = max(20.0, r_targets * 0.8 + 20.0)
            att = np.clip(rng.normal(loc=base_att * team_scale, scale=5.0, size=n_sims), 5.0, None)
            cmp = rng.binomial(n=att.astype(int), p=min(0.75, max(0.45, r_cr * d_mult)))
            pass_yards = rng.normal(loc=cmp * eff_ypt, scale=np.sqrt(np.maximum(1.0, cmp)) * (eff_ypt * 0.35))
            pass_yards = np.clip(pass_yards, 0.0, None)
            pass_tds = rng.binomial(n=att.astype(int), p=max(0.0, min(0.15, r_pass_td * d_mult + 0.1 * r_rz_add)))
            pass_pts = _DK_PASS_YARD_PTS * pass_yards + _DK_PASS_TD_PTS * pass_tds

        pts = (
            _DK_REC_PTS * rec
            + _DK_RUSH_REC_YARD_PTS * (rec_yards + rush_yards)
            + _DK_RUSH_REC_TD_PTS * (rec_tds + rush_tds)
            + pass_pts
        )

        means[i] = float(np.mean(pts))
        p20s[i] = float(np.percentile(pts, 20))
        p80s[i] = float(np.percentile(pts, 80))
        p90s[i] = float(np.percentile(pts, 90))

    return pd.DataFrame({
        "mean": means,
        "p20": p20s,
        "p80": p80s,
        "p90": p90s,
    }, index=df.index)

