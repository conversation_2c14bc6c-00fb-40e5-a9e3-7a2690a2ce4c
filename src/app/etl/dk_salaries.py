import pandas as pd
DK_KEEP = ["Position","Name","Salary","TeamAbbrev","AvgPointsPerGame","GameInfo","Roster Position"]
def load_dk(path: str) -> pd.DataFrame:
    df = pd.read_csv(path)
    df = df[[c for c in DK_KEEP if c in df.columns]].copy()
    df.rename(columns={"TeamAbbrev":"team","Name":"name","Salary":"salary","Position":"pos"}, inplace=True)
    df["name"]=df["name"].astype(str); df["team"]=df["team"].astype(str); df["pos"]=df["pos"].astype(str)
    return df

