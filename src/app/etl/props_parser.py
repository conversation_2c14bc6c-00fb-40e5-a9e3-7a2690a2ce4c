import pandas as pd
from ..utils.normalize import normalize_team, normalize_name
def build_props_table(event_json: list, event_meta: dict) -> pd.DataFrame:
    rows=[]
    for book in event_json:
        for m in book.get("markets", []):
            mkey = m.get("key")
            for out in m.get("outcomes", []):
                player = normalize_name(out.get("description","").strip())
                line = out.get("point")
                if line is None: continue
                rows.append({"player":player,"market":mkey,"book":book.get("title"),"line":float(line)})
    if not rows: return pd.DataFrame(columns=["player","market","consensus"])
    df = pd.DataFrame(rows)
    agg = df.groupby(["player","market"])["line"].median().reset_index()
    pivot = agg.pivot(index="player", columns="market", values="line").reset_index()
    pivot["home_team"] = normalize_team(event_meta["home_team"])
    pivot["away_team"] = normalize_team(event_meta["away_team"])
    return pivot

