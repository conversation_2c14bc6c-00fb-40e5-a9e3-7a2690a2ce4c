import numpy as np, pandas as pd
DK = {"pass_yd":0.04,"pass_td":4.0,"int":-1.0,"rush_yd":0.1,"rec_yd":0.1,"rec":1.0,"rush_td":6.0,"rec_td":6.0}
def proj_from_props(props: pd.DataFrame) -> pd.DataFrame:
    df = props.copy()
    df["pass_yds"]=df.get("player_pass_yards", pd.Series(np.nan, index=df.index))
    df["pass_tds"]=df.get("player_pass_tds",  pd.Series(np.nan, index=df.index))
    df["rush_yds"]=df.get("player_rush_yards",pd.Series(np.nan, index=df.index))
    df["rec_yds"] =df.get("player_rec_yards",  pd.Series(np.nan, index=df.index))
    df["recs"]   =df.get("player_receptions",  pd.Series(np.nan, index=df.index))
    def guess_pos(r):
        if not np.isnan(r["pass_yds"]) or not np.isnan(r["pass_tds"]): return "QB"
        if not np.isnan(r["recs"]): return "WR/TE"
        if not np.isnan(r["rush_yds"]): return "RB"
        return "UNK"
    df["pos_guess"]=df.apply(guess_pos, axis=1)
    dk = (df["pass_yds"].fillna(0)*DK["pass_yd"] + df["pass_tds"].fillna(0)*DK["pass_td"] +
          df["rush_yds"].fillna(0)*DK["rush_yd"] + df["rec_yds"].fillna(0)*DK["rec_yd"] +
          df["recs"].fillna(0)*DK["rec"])
    df["dk_proj"]=dk
    df["dk_proj"] += np.where(df["pass_yds"]>=290, 1.0, 0.0)
    df["dk_proj"] += np.where(df["rush_yds"]>=90, 0.7, 0.0)
    df["dk_proj"] += np.where(df["rec_yds"]>=90, 0.7, 0.0)
    return df[["player","pos_guess","dk_proj","home_team","away_team"]]

