from pathlib import Path
import orjson, hashlib
DATA = Path("data"); CACHE = DATA / "cache"; CACHE.mkdir(parents=True, exist_ok=True)
def json_dump(obj, path: Path): path.parent.mkdir(parents=True, exist_ok=True); path.write_bytes(orjson.dumps(obj))
def json_load(path: Path): return orjson.loads(path.read_bytes())
def cache_key(name: str) -> Path: return CACHE / (hashlib.md5(name.encode()).hexdigest() + ".json")

