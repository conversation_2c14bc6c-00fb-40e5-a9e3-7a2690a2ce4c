import json, re
from pathlib import Path
from rapidfuzz import fuzz
TEAM_MAP = json.loads(Path("config/team_map.json").read_text())
def normalize_team(s: str) -> str | None:
    s_up = re.sub(r"[^A-Z ]","", s.upper())
    best, score = None, -1
    for k, aliases in TEAM_MAP.items():
        for a in aliases:
            sc = fuzz.token_sort_ratio(s_up, a)
            if sc > score:
                best, score = k, sc
    return best if score >= 75 else None
def normalize_name(name: str, alias_csv="config/player_aliases.csv") -> str:
    try:
        import pandas as pd
        df = pd.read_csv(alias_csv)
        hit = df[df.alias.str.lower()==name.lower()]
        if not hit.empty: return hit.iloc[0].canonical
    except Exception: pass
    return re.sub(r"\s+"," ", name).strip()

