import argparse, sys, pandas as pd
from rich import print
from app.io.odds import list_events, event_odds
from app.etl.dk_salaries import load_dk
from app.etl.props_parser import build_props_table
from app.models.dk_points import proj_from_props
from app.io.fc_export import to_fc_csv
from app.utils.normalize import normalize_team

def run(dk_csv: str, out_csv: str):
    dk = load_dk(dk_csv)
    teams = sorted(set(dk["team"].dropna().unique().tolist()))
    print(f"[green]DK teams on slate:[/green] {teams}")
    events = list_events()
    matched=[]
    for ev in events:
        home = normalize_team(ev["home_team"]); away = normalize_team(ev["away_team"])
        if home in teams or away in teams:
            matched.append({"event_id":ev["id"],"home_team":ev["home_team"],"away_team":ev["away_team"]})
    if not matched:
        print("[red]No matching events found from DK slate[/red]"); sys.exit(1)
    prop_frames=[]
    for em in matched:
        odds = event_odds(em["event_id"])
        pf = build_props_table(odds, em)
        prop_frames.append(pf)
    props = pd.concat(prop_frames, ignore_index=True) if prop_frames else pd.DataFrame()
    proj = proj_from_props(props) if not props.empty else pd.DataFrame(columns=["player","dk_proj"])
    out = to_fc_csv(dk, proj, out_csv)
    print(f"[bold green]Exported {len(out)} rows → {out_csv}[/bold green]")

if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--dk", required=True, help="Path to DraftKings salaries CSV")
    ap.add_argument("--out", default="data/outputs/fc_weekXX.csv")
    args = ap.parse_args()
    run(args.dk, args.out)

