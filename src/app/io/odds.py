import httpx, time
from tenacity import retry, stop_after_attempt, wait_exponential
from . import secrets
from ..utils.io import cache_key, json_dump, json_load
BASE = "https://api.the-odds-api.com/v4"
API_KEY = secrets.odds_api_key()

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=0.8, max=4))
def _get(url, params):
    with httpx.Client(timeout=15) as c:
        r = c.get(url, params=params); r.raise_for_status(); return r.json()

def list_events(league="americanfootball_nfl"):
    url = f"{BASE}/sports/{league}/events"; params = {"apiKey": API_KEY}
    key = cache_key(f"events:{league}:{time.strftime('%Y-%m-%d')}")
    try: return json_load(key)
    except Exception: pass
    data = _get(url, params); json_dump(data, key); return data

def event_odds(event_id: str, markets=("player_pass_yards","player_rush_yards","player_receptions","player_rec_yards","player_pass_tds","player_rush_attempts"), regions="us,us2", oddsFormat="american"):
    url = f"{BASE}/sports/americanfootball_nfl/events/{event_id}/odds"
    params = {"apiKey": API_KEY, "regions": regions, "markets": ",".join(markets), "oddsFormat": oddsFormat}
    key = cache_key(f"event_odds:{event_id}:{','.join(markets)}:{regions}:{oddsFormat}")
    try: return json_load(key)
    except Exception: pass
    data = _get(url, params); json_dump(data, key); return data

def team_totals(event_id: str, regions="us,us2", oddsFormat="american"):
    """Fetch team totals (over/under) for a specific game."""
    url = f"{BASE}/sports/americanfootball_nfl/events/{event_id}/odds"
    params = {"apiKey": API_KEY, "regions": regions, "markets": "totals", "oddsFormat": oddsFormat}
    key = cache_key(f"team_totals:{event_id}:{regions}:{oddsFormat}")
    try: return json_load(key)
    except Exception: pass
    data = _get(url, params); json_dump(data, key); return data



