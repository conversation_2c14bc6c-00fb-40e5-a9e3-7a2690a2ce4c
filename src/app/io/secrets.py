import os
from pathlib import Path

# Load .env file if it exists
def _load_env():
    # Go up from src/app/io/secrets.py to project root
    env_path = Path(__file__).parent.parent.parent.parent / ".env"
    if env_path.exists():
        with open(env_path) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

# Load environment variables on import
_load_env()

def odds_api_key() -> str:
    k = os.getenv("ODDS_API_KEY")
    if not k: raise RuntimeError("ODDS_API_KEY not set")
    return k

