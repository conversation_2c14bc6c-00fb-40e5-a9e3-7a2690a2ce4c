import pandas as pd
def to_fc_csv(dk_salaries: pd.DataFrame, projections: pd.DataFrame, out_path: str):
    s = dk_salaries.copy()
    s["name_norm"] = s["name"].str.replace(r"\s+"," ", regex=True).str.strip().str.lower()
    p = projections.copy()
    p["name_norm"] = p["player"].str.lower()
    m = s.merge(p[["name_norm","dk_proj"]], on="name_norm", how="left")
    m["Projection"] = m["dk_proj"].fillna(0.0)
    out = m.rename(columns={"name":"Name","team":"Team","pos":"Position","salary":"Salary"})[
        ["Name","Team","Position","Salary","Projection"]
    ]
    out.to_csv(out_path, index=False)
    return out

