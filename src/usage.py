from __future__ import annotations

from typing import Dict

# Static default role-based usage shares
# Simple, team-agnostic defaults that can later be overridden by external CSVs
# Shares are expressed as fractions (0.0 - 1.0) within the relevant usage pool


def get_qb_share(team: str | None = None) -> Dict[str, float]:
    """Return default QB dropback shares for a team.

    For NFL, assume one primary QB handles ~100% of dropbacks.
    Keys correspond to depth slots we will map by team ranking.
    """
    return {
        "QB1": 1.00,
        "QB2": 0.00,
    }


def get_rb_share(team: str | None = None) -> Dict[str, float]:
    """Return default team RB rush attempt shares.

    Rough baseline split for early season without injuries/news.
    """
    return {
        "RB1": 0.65,
        "RB2": 0.25,
        "RB3": 0.10,
        # Any additional backs (FB, RB4) implicitly 0.0
    }


def get_wr_te_share(team: str | None = None) -> Dict[str, float]:
    """Return default team target share distribution for WR/TE group.

    Target shares across primary pass catchers. These do not need to sum to 1.0
    across all possible depth slots; unlisted depth slots are treated as 0.0.
    """
    return {
        # Wide Receivers
        "WR1": 0.25,
        "WR2": 0.20,
        "WR3": 0.15,
        "WR4": 0.10,
        # Tight Ends
        "TE1": 0.18,
        "TE2": 0.07,
    }



# --- Week usage CSV loader for overrides ---
from pathlib import Path
import pandas as pd
from rapidfuzz import fuzz

def load_week_usage_csv(week: int, season: int = 2025) -> pd.DataFrame:
    """Load optional usage CSV: data/nfl_stats/{season}/week{W}_*.csv

    Expected columns (any subset accepted):
      dk_player_id,name,team,position,role,share,snap_pct,targets_g,rush_att_g,routes_g

    Returns normalized columns: name_norm, TEAM, POSITION, ROLE, share, snap_pct, targets_g, rush_att_g, routes_g
    """
    # Try season-specific directory first
    season_dir = Path("data/nfl_stats") / str(season)
    if season_dir.exists():
        # Look for any CSV files matching week pattern
        pattern = f"week{week}_*.csv"
        csv_files = list(season_dir.glob(pattern))
        if csv_files:
            # Use the first matching file
            p = csv_files[0]
            try:
                df = pd.read_csv(p)
            except Exception:
                return pd.DataFrame()
            if df.empty:
                return pd.DataFrame()
            cols = {c.lower(): c for c in df.columns}
            # Normalize identifiers
            out = pd.DataFrame()
            if "name" in cols:
                out["name_norm"] = df[cols["name"]].astype(str).str.replace(r"\s+"," ", regex=True).str.strip().str.lower()
            else:
                out["name_norm"] = ""
            if "team" in cols:
                out["TEAM"] = df[cols["team"]].astype(str).str.upper().str.strip()
            else:
                out["TEAM"] = ""
            if "position" in cols:
                out["POSITION"] = df[cols["position"]].astype(str).str.upper().str.strip()
            else:
                out["POSITION"] = ""
            if "role" in cols:
                out["ROLE"] = df[cols["role"]].astype(str).str.upper().str.strip()
            else:
                out["ROLE"] = ""
            # Numeric fields
            def _num(col, default=0.0):
                return pd.to_numeric(df.get(cols.get(col, ""), pd.Series([default]*len(df))), errors="coerce")
            out["share"] = _num("share", 0.0)
            out["snap_pct"] = _num("snap_pct", 0.7)
            out["targets_g"] = _num("targets_g", 0.0)
            out["rush_att_g"] = _num("rush_att_g", 0.0)
            out["routes_g"] = _num("routes_g", 0.0)
            if "dk_player_id" in cols:
                out["ID"] = df[cols["dk_player_id"]]
            return out
    
    # Fallback to old location for backwards compatibility
    p = Path("data/usage") / f"week{week}_usage.csv"
    if not p.exists():
        return pd.DataFrame()
    try:
        df = pd.read_csv(p)
    except Exception:
        return pd.DataFrame()
    if df.empty:
        return pd.DataFrame()
    cols = {c.lower(): c for c in df.columns}
    # Normalize identifiers
    out = pd.DataFrame()
    if "name" in cols:
        out["name_norm"] = df[cols["name"]].astype(str).str.replace(r"\s+"," ", regex=True).str.strip().str.lower()
    else:
        out["name_norm"] = ""
    if "team" in cols:
        out["TEAM"] = df[cols["team"]].astype(str).str.upper().str.strip()
    else:
        out["TEAM"] = ""
    if "position" in cols:
        out["POSITION"] = df[cols["position"]].astype(str).str.upper().str.strip()
    else:
        out["POSITION"] = ""
    if "role" in cols:
        out["ROLE"] = df[cols["role"]].astype(str).str.upper().str.strip()
    else:
        out["ROLE"] = ""
    # Numeric fields
    def _num(col, default=0.0):
        return pd.to_numeric(df.get(cols.get(col, ""), pd.Series([default]*len(df))), errors="coerce")
    out["share"] = _num("share", 0.0)
    out["snap_pct"] = _num("snap_pct", 0.7)
    out["targets_g"] = _num("targets_g", 0.0)
    out["rush_att_g"] = _num("rush_att_g", 0.0)
    out["routes_g"] = _num("routes_g", 0.0)
    if "dk_player_id" in cols:
        out["ID"] = df[cols["dk_player_id"]]
    return out
