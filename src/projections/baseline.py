from __future__ import annotations

import math
from typing import Any, Dict

import numpy as np
import pandas as pd


# -----------------
# Small, unit-friendly helpers
# -----------------

def clamp(x: float, lo: float, hi: float) -> float:
    """Clamp scalar x to [lo, hi].

    Works with NaN by treating NaN as default mid value.
    """
    if x is None or (isinstance(x, float) and np.isnan(x)):
        # Midpoint as a conservative default
        return (lo + hi) / 2.0
    return max(lo, min(hi, float(x)))


def logistic(x: float) -> float:
    """Stable logistic transform for probability modeling."""
    # guard for large magnitudes to avoid overflow
    if x > 36:
        return 1.0
    if x < -36:
        return 0.0
    return 1.0 / (1.0 + math.exp(-x))


# -----------------
# Baseline projection core
# -----------------

_DK_PASS_YARD_PTS = 0.04
_DK_RUSH_REC_YARD_PTS = 0.1
_DK_PASS_TD_PTS = 4.0
_DK_RUSH_REC_TD_PTS = 6.0
_DK_REC_PTS = 1.0


def _get(row: pd.Series, key: str, default: Any) -> Any:
    # pd.Series.get will return None for missing without default; provide default explicitly
    val = row.get(key, default)
    return default if val is None or (isinstance(val, float) and np.isnan(val)) else val


def _safe_float(row: pd.Series, key: str, default: float) -> float:
    try:
        v = _get(row, key, default)
        return float(v)
    except Exception:
        return float(default)


def _team_touchdowns_from_implied(row: pd.Series) -> float:
    implied_total = _safe_float(row, "implied_total", 21.0)
    # Convert implied points to touchdowns crudely (~7 pts per TD inc. XP)
    return max(0.0, implied_total / 7.0)


def _qb_baseline(row: pd.Series) -> Dict[str, Any]:
    team_plays = _safe_float(row, "team_plays", 60.0)
    pass_rate = clamp(_safe_float(row, "pass_rate", 0.56), 0.2, 0.85)
    rush_rate = clamp(1.0 - pass_rate, 0.15, 0.8)

    pass_att = team_plays * pass_rate
    ypa = clamp(_safe_float(row, "yards_per_att", _safe_float(row, "pass_ypa", 7.2)), 4.5, 9.5)
    ptr = clamp(_safe_float(row, "pass_td_rate", 0.045), 0.01, 0.09)

    qb_rush_share = clamp(_safe_float(row, "rush_share_qb", _safe_float(row, "qb_rush_share", 0.1)), 0.0, 0.5)
    ypc = clamp(_safe_float(row, "yards_per_carry", _safe_float(row, "rush_ypa", 4.6)), 3.2, 7.5)
    rtr = clamp(_safe_float(row, "rush_td_rate_qb", 0.02), 0.0, 0.12)

    pass_yards = pass_att * ypa
    pass_tds = pass_att * ptr

    rush_att = team_plays * rush_rate * qb_rush_share
    rush_yards = rush_att * ypc
    rush_tds = rush_att * rtr

    dk = (
        _DK_PASS_YARD_PTS * pass_yards
        + _DK_PASS_TD_PTS * pass_tds
        + _DK_RUSH_REC_YARD_PTS * rush_yards
        + _DK_RUSH_REC_TD_PTS * rush_tds
    )

    return {
        "baseline_dk": float(dk),
        "minutes_or_snaps": float(team_plays * clamp(_safe_float(row, "snap_share", 0.98), 0.5, 1.0)),
        "metadata": {
            "pass_att": pass_att,
            "pass_yards": pass_yards,
            "pass_tds": pass_tds,
            "rush_att": rush_att,
            "rush_yards": rush_yards,
            "rush_tds": rush_tds,
        },
    }


def _skill_baseline(row: pd.Series, pos: str) -> Dict[str, Any]:
    # Common inputs
    team_plays = _safe_float(row, "team_plays", 60.0)
    pass_rate = clamp(_safe_float(row, "pass_rate", 0.56), 0.2, 0.85)
    rush_rate = clamp(1.0 - pass_rate, 0.15, 0.8)

    # Usage shares
    target_share = clamp(_safe_float(row, "target_share", 0.18), 0.0, 0.5)
    rush_share = clamp(_safe_float(row, "rush_share", 0.0 if pos in ("WR", "TE") else 0.55), 0.0, 0.9)

    # Efficiency
    ypt = clamp(_safe_float(row, "yards_per_tgt", 7.8 if pos == "WR" else 7.2), 4.0, 12.0)
    catch_rate = clamp(_safe_float(row, "catch_rate", 0.65), 0.4, 0.9)
    aDOT = clamp(_safe_float(row, "aDOT", 9.0 if pos == "WR" else 7.5), 3.0, 18.0)

    ypc = clamp(_safe_float(row, "yards_per_carry", 4.4), 3.2, 7.0)

    # Red-zone shares
    rz_tgt_share = clamp(_safe_float(row, "rz_tgt_share", target_share * 0.9), 0.0, 0.8)
    rz_rush_share = clamp(_safe_float(row, "rz_rush_share", rush_share * 0.9), 0.0, 0.9)

    # Team touchdown prior
    team_tds = _team_touchdowns_from_implied(row)

    # Receiving volume
    targets = team_plays * pass_rate * target_share
    receptions = targets * catch_rate
    rec_yards = targets * ypt

    # Receiving TDs: blend rate-based estimate with red-zone based share of team passing TDs
    rec_td_rate = clamp(_safe_float(row, "rec_td_rate", 0.045 if pos == "WR" else 0.035), 0.0, 0.12)
    rate_td_rec = targets * rec_td_rate
    rz_td_rec = team_tds * pass_rate * rz_tgt_share
    rec_tds = 0.5 * rate_td_rec + 0.5 * rz_td_rec

    # Rushing volume (RB primarily; WR/TE rarely rush)
    rush_att = team_plays * rush_rate * rush_share
    rush_yards = rush_att * ypc

    # Rushing TDs: blend rate-based with red-zone share
    rush_td_rate = clamp(_safe_float(row, "rush_td_rate", 0.03 if pos == "RB" else 0.005), 0.0, 0.12)
    rate_td_rush = rush_att * rush_td_rate
    rz_td_rush = team_tds * rush_rate * rz_rush_share
    rush_tds = 0.5 * rate_td_rush + 0.5 * rz_td_rush

    dk = (
        _DK_REC_PTS * receptions
        + _DK_RUSH_REC_YARD_PTS * (rec_yards + rush_yards)
        + _DK_RUSH_REC_TD_PTS * (rec_tds + rush_tds)
    )

    # Volatility prior: scale with aDOT and role concentration
    role_conc = max(target_share, rush_share)
    vol = max(1.0, 0.25 * float(dk) + 0.1 * (aDOT / 10.0) - 0.5 * role_conc)

    return {
        "baseline_dk": float(dk),
        "minutes_or_snaps": float(team_plays * clamp(_safe_float(row, "snap_share", 0.75), 0.2, 1.0)),
        "volatility_prior": float(vol),
        "metadata": {
            "targets": targets,
            "receptions": receptions,
            "rec_yards": rec_yards,
            "rec_tds": rec_tds,
            "rush_att": rush_att,
            "rush_yards": rush_yards,
            "rush_tds": rush_tds,
        },
    }


def _dst_k_baseline(row: pd.Series, pos: str) -> Dict[str, Any]:
    # Simple prior based on opponent EPA allowed and spread; this is intentionally a stub
    opp_def_epa = _safe_float(row, "opp_off_epa_per_play", -0.02)
    spread = _safe_float(row, "spread", 0.0)  # team spread; negative means favored

    # Map to a small prior using a logistic transform
    strength = logistic(-4.0 * opp_def_epa + 0.08 * (-spread))

    if pos == "DST":
        # DST DK scoring is complex; a simple baseline around 6-10
        dk = 4.0 + 6.0 * strength
    else:  # K
        # Kicker depends on implied total and spread advantage
        implied_total = _safe_float(row, "implied_total", 21.0)
        dk = clamp(2.0 + 0.18 * implied_total + 0.5 * logistic(-0.2 * spread), 3.0, 14.0)

    return {
        "baseline_dk": float(dk),
        "minutes_or_snaps": 60.0,  # game minutes proxy
        "metadata": {"strength": strength},
    }


def compute_baseline_player_projection(row: pd.Series, league_context: dict) -> Dict[str, Any]:
    """Compute a deterministic DK baseline projection for a single player row.

    The row is expected to contain: player_id, slate_id, pos, team, opp, salary (optional),
    team_plays, pass_rate, usage shares (rush_share, target_share, aDOT), efficiency metrics
    (yards per attempt/target/carry, TD rates), snap_share, implied_total, spread,
    opponent EPA/allowed, weather/roof flags (ignored here but accepted).
    """
    pos = str(_get(row, "pos", _get(row, "position", ""))).upper()

    if pos == "QB":
        comp = _qb_baseline(row)
    elif pos in ("RB", "WR", "TE"):
        comp = _skill_baseline(row, pos)
    elif pos in ("DST", "D", "DEF", "K"):
        comp = _dst_k_baseline(row, "DST" if pos in ("DST", "D", "DEF") else "K")
    else:
        # Unknown position: conservative minimal projection
        comp = {"baseline_dk": 0.0, "minutes_or_snaps": _safe_float(row, "team_plays", 60.0) * 0.1, "metadata": {}}

    out = {
        "player_id": _get(row, "player_id", _get(row, "id", None)),
        "slate_id": _get(row, "slate_id", None),
        "team": _get(row, "team", _get(row, "tm", None)),
        "opp": _get(row, "opp", _get(row, "opponent", None)),
        "pos": pos,
        "baseline_dk": float(comp.get("baseline_dk", 0.0)),
        "minutes_or_snaps": float(comp.get("minutes_or_snaps", 0.0)),
        "volatility_prior": float(comp.get("volatility_prior", max(1.0, 0.2 * float(comp.get("baseline_dk", 0.0))))),
        "metadata": comp.get("metadata", {}),
    }
    return out


def compute_baselines(df_joined: pd.DataFrame, league_context: dict) -> pd.DataFrame:
    """Compute baselines for all players in the joined feature table.

    This performs row-wise deterministic computations. NaNs are filled with
    conservative defaults within the per-row function. No network calls.
    """
    if df_joined is None or len(df_joined) == 0:
        return pd.DataFrame(columns=[
            "player_id",
            "slate_id",
            "team",
            "opp",
            "pos",
            "baseline_dk",
            "minutes_or_snaps",
            "volatility_prior",
            "metadata",
        ])

    records = [compute_baseline_player_projection(row, league_context) for _, row in df_joined.iterrows()]
    return pd.DataFrame.from_records(records)

