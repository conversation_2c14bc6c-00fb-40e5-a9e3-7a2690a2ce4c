from __future__ import annotations

import os
import json
import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Tuple
import logging
from rapidfuzz import process, fuzz
from datetime import datetime
import time
import random
from tenacity import retry, stop_after_attempt, wait_exponential_jitter

logger = logging.getLogger(__name__)


def fetch_props_for_week(season: int, week: int, *, force: bool = False) -> pd.DataFrame:
    """Robust fetch routine that pulls all NFL player props for a given week.

    Behavior:
    1) Build the slate: get all NFL games for the week
    2) For each game, call the Odds API player-props endpoint with comprehensive markets
    3) Normalize names using existing alias table and fuzzy matching
    4) Produce a tall DataFrame with normalized data
    5) Write raw JSON per game for auditing and normalized CSV
    6) Return the normalized DataFrame

    Args:
        season: NFL season year
        week: NFL week number
        force: Force refresh of cached data

    Returns:
        DataFrame with columns: player, team, opponent, market, line, over_odds, under_odds, book, game_id, season, week, retrieved_at
    """
    # Check cache first unless force refresh
    output_csv = f"data/props/props_{season}_week{week}.csv"
    if not force and os.path.exists(output_csv):
        logger.info(f"Using cached props data from {output_csv}")
        return pd.read_csv(output_csv)

    # Require Odds API client - no fallback
    try:
        from src.app.io.odds import list_events, event_odds
        from src.app.utils.normalize import normalize_team
    except ImportError as e:
        raise ImportError("Odds API client is required but not available") from e

    # Create output directories
    raw_dir = f"data/props/raw/{season}-week{week}"
    os.makedirs(raw_dir, exist_ok=True)
    os.makedirs("data/props", exist_ok=True)

    try:
        # Step 1: Build the slate - get all NFL games for the week
        logger.info(f"Fetching NFL games for {season} week {week}")
        events = list_events("americanfootball_nfl")
        if not events:
            raise ValueError("No NFL events found from Odds API - cannot proceed")

        # Filter events for the current week (basic filtering by date proximity)
        # In a production system, you'd want more sophisticated week filtering
        nfl_games = []
        for event in events:
            if event.get('id') and event.get('home_team') and event.get('away_team'):
                nfl_games.append(event)

        if not nfl_games:
            raise ValueError("No valid NFL games found - cannot proceed")

        logger.info(f"Found {len(nfl_games)} NFL games to process")

        # Step 2: For each game, fetch comprehensive player props
        all_props = []

        # Define comprehensive markets to fetch - using actual available market names from API
        markets = [
            "player_pass_yds", "player_pass_tds", "player_pass_completions", "player_pass_attempts", "player_pass_interceptions",
            "player_rush_yds", "player_rush_tds", "player_rush_attempts",
            "player_reception_yds", "player_receptions",  # Note: it's player_reception_yds, not player_receiving_yds
            "player_anytime_td", "player_1st_td", "player_last_td",
            "player_sacks", "player_tackles_assists"
        ]

        for i, game in enumerate(nfl_games):
            game_id = game['id']
            home_team = game['home_team']
            away_team = game['away_team']

            logger.info(f"Processing game {i+1}/{len(nfl_games)}: {away_team} @ {home_team}")

            try:
                # Fetch props for this game with rate limiting
                game_props = _fetch_game_props_with_retry(game_id, markets, home_team, away_team)

                if game_props:
                    # Save raw JSON for auditing
                    raw_file = os.path.join(raw_dir, f"game_{game_id}_{away_team}_at_{home_team}.json")
                    with open(raw_file, 'w') as f:
                        json.dump(game_props, f, indent=2)

                    all_props.extend(game_props)
                    logger.info(f"Fetched {len(game_props)} props for {away_team} @ {home_team}")
                else:
                    logger.warning(f"No props found for {away_team} @ {home_team}")

                # Rate limiting with jitter
                time.sleep(random.uniform(0.5, 1.5))

            except Exception as e:
                logger.error(f"Error fetching props for game {game_id}: {e}")
                continue

        if not all_props:
            raise ValueError("No props data collected from any games - API may be down or no markets available")

        # Step 3: Normalize and create DataFrame
        logger.info(f"Processing {len(all_props)} total prop records")
        props_df = pd.DataFrame(all_props)

        # Add metadata
        props_df['season'] = season
        props_df['week'] = week
        props_df['retrieved_at'] = datetime.utcnow().isoformat()

        # Step 4: Normalize Anytime TD markets
        props_df = _normalize_anytime_td_markets(props_df)

        # Step 5: Normalize player names
        props_df = _normalize_player_names(props_df)

        # Step 6: Save normalized CSV
        props_df.to_csv(output_csv, index=False)
        logger.info(f"Saved {len(props_df)} normalized props to {output_csv}")

        # Step 7: Print audit summary
        _print_audit_summary(props_df)

        return props_df

    except Exception as e:
        logger.error(f"Error in fetch_props_for_week: {e}")
        raise  # No fallback - re-raise the error


@retry(stop=stop_after_attempt(3), wait=wait_exponential_jitter(initial=1, max=8))
def _fetch_game_props_with_retry(game_id: str, markets: List[str], home_team: str, away_team: str) -> List[Dict]:
    """Fetch props for a single game with exponential backoff and jitter.

    This function needs to be updated to match the actual Odds API response structure.
    The current implementation is a placeholder that needs real API integration.
    """
    try:
        from src.app.io.odds import event_odds
        from src.app.utils.normalize import normalize_team
    except ImportError:
        return []

    try:
        logger.info(f"Fetching props for {away_team} @ {home_team} with markets: {markets}")

        # Call the Odds API with proper regions and format
        odds_data = event_odds(game_id, markets=tuple(markets), regions="us,us2", oddsFormat="american")

        if not odds_data:
            logger.warning(f"No odds data returned for game {game_id}")
            return []

        logger.info(f"Raw odds data structure: {type(odds_data)} with keys: {odds_data.keys() if isinstance(odds_data, dict) else 'Not a dict'}")

        # Get roster mapping for proper team assignment
        roster_map = _get_team_rosters_for_game(home_team, away_team)

        game_props = []

        # Parse the actual Odds API v4 response structure
        if isinstance(odds_data, dict) and 'bookmakers' in odds_data:
            for bookmaker in odds_data['bookmakers']:
                book_name = bookmaker.get('key', 'unknown')

                for market in bookmaker.get('markets', []):
                    market_key = market.get('key', '')

                    # Group outcomes by player for Over/Under pairs
                    player_outcomes = {}
                    for outcome in market.get('outcomes', []):
                        player_name = outcome.get('description', '').strip()
                        if not player_name:
                            continue

                        if player_name not in player_outcomes:
                            player_outcomes[player_name] = {}

                        outcome_type = outcome.get('name', '').strip()
                        player_outcomes[player_name][outcome_type] = {
                            'price': outcome.get('price'),
                            'point': outcome.get('point')
                        }

                    # Create prop records for each player
                    for player_name, outcomes in player_outcomes.items():
                        # Assign player to correct team
                        team, opponent = _assign_player_to_team(player_name, home_team, away_team, roster_map)

                        # Extract line and odds
                        line = None
                        over_odds = None
                        under_odds = None

                        if 'Over' in outcomes:
                            over_odds = outcomes['Over']['price']
                            line = outcomes['Over']['point']
                        if 'Under' in outcomes:
                            under_odds = outcomes['Under']['price']
                            if line is None:
                                line = outcomes['Under']['point']

                        # For binary markets (anytime_td), use different structure
                        if market_key in ['player_anytime_td', 'player_first_td', 'player_last_td']:
                            # These are typically Yes/No markets
                            if 'Yes' in outcomes:
                                over_odds = outcomes['Yes']['price']
                                line = 1.0
                            elif player_name in [o.get('description', '') for o in market.get('outcomes', [])]:
                                # Sometimes anytime TD is just the player name with odds
                                for outcome in market.get('outcomes', []):
                                    if outcome.get('description') == player_name:
                                        over_odds = outcome.get('price')
                                        line = 1.0
                                        break

                        prop_record = {
                            'player': player_name,
                            'team': team,
                            'opponent': opponent,
                            'market': market_key,
                            'line': line,
                            'over_odds': over_odds,
                            'under_odds': under_odds,
                            'book': book_name,
                            'game_id': game_id
                        }

                        # Only add if we have essential data
                        if player_name and market_key and line is not None:
                            game_props.append(prop_record)

        logger.info(f"Extracted {len(game_props)} prop records from game {game_id}")
        return game_props

    except Exception as e:
        logger.error(f"Error fetching props for game {game_id}: {e}")
        logger.error(f"Error type: {type(e)}")
        if hasattr(e, 'response'):
            logger.error(f"HTTP Status: {e.response.status_code}")
            logger.error(f"Response text: {e.response.text}")
        raise  # Re-raise for retry logic


def _get_team_rosters_for_game(home_team: str, away_team: str) -> Dict[str, str]:
    """Get player-to-team mapping using the FC/DK data which already has correct team assignments.

    Returns a mapping of player_name -> team_abbreviation
    """
    try:
        from .dk_nfl import discover_week_csv, read_dk_nfl_csv

        # Get roster info from DK CSV which has the correct team data
        dk_path = discover_week_csv(4)
        if os.path.exists(dk_path):
            dk_df = read_dk_nfl_csv(dk_path)

            # Create player -> team mapping for ALL players (not just game teams)
            # The Team column in the FC data is the authoritative source
            player_team_map = {}
            for _, row in dk_df.iterrows():
                player_name = str(row.get('Name', row.get('Player', ''))).strip()
                team = str(row.get('Team', '')).strip()
                if player_name and team:
                    # Store both the original name and a cleaned version
                    player_team_map[player_name.lower()] = team
                    # Also try without punctuation/special chars
                    clean_name = player_name.lower().replace("'", "").replace(".", "").replace("-", " ")
                    player_team_map[clean_name] = team

            logger.info(f"Built roster map from FC data: {len(player_team_map)} player entries")
            return player_team_map

    except Exception as e:
        logger.warning(f"Could not build roster map from FC data: {e}")

    return {}


def _assign_player_to_team(player_name: str, home_team: str, away_team: str, roster_map: Dict[str, str]) -> Tuple[str, str]:
    """Assign a player to the correct team using FC data and determine opponent."""
    if not player_name:
        return home_team, away_team

    # Team abbreviation to full name mapping
    team_abbrev_to_full = {
        'MIN': 'Minnesota Vikings',
        'PIT': 'Pittsburgh Steelers',
        'DET': 'Detroit Lions',
        'CLE': 'Cleveland Browns',
        'NYG': 'New York Giants',
        'LAC': 'Los Angeles Chargers',
        'ATL': 'Atlanta Falcons',
        'WAS': 'Washington Commanders',
        'KC': 'Kansas City Chiefs',
        'BAL': 'Baltimore Ravens',
        'LV': 'Las Vegas Raiders',
        'CHI': 'Chicago Bears',
        'DAL': 'Dallas Cowboys',
        'GB': 'Green Bay Packers',
        'MIA': 'Miami Dolphins',
        'NYJ': 'New York Jets',
        'DEN': 'Denver Broncos',
        'CIN': 'Cincinnati Bengals'
    }

    # Clean player name for matching
    player_key = player_name.lower().strip()

    # Try exact match first
    if player_key in roster_map:
        team_abbrev = roster_map[player_key]
        # Convert abbreviation to full team name
        full_team_name = team_abbrev_to_full.get(team_abbrev.upper(), team_abbrev)

        # Determine opponent - if this player's team matches home team, opponent is away team
        if full_team_name == home_team:
            return home_team, away_team
        elif full_team_name == away_team:
            return away_team, home_team
        else:
            # Player's team doesn't match either home or away - use full name and guess opponent
            return full_team_name, away_team if full_team_name != home_team else home_team

    # Try fuzzy matching on last name
    player_parts = player_name.split()
    if len(player_parts) >= 2:
        last_name = player_parts[-1].lower()
        for roster_player, team_abbrev in roster_map.items():
            if last_name in roster_player:
                # Convert abbreviation to full team name
                full_team_name = team_abbrev_to_full.get(team_abbrev.upper(), team_abbrev)

                # Determine opponent
                if full_team_name == home_team:
                    return home_team, away_team
                elif full_team_name == away_team:
                    return away_team, home_team
                else:
                    return full_team_name, away_team if full_team_name != home_team else home_team

    # Default to home team if no match found
    logger.warning(f"Could not assign player '{player_name}' to team, defaulting to {home_team}")
    return home_team, away_team


def _enhance_odds_api_integration():
    """Enhance the existing Odds API integration to support more markets and regions.

    This function documents what needs to be done to improve the API integration.
    """
    improvements_needed = [
        "1. Update src/app/io/odds.py to support regions='us,us2' parameter",
        "2. Add support for oddsFormat='american' parameter",
        "3. Expand default markets to include all player prop types",
        "4. Add proper error handling for rate limits and API quotas",
        "5. Implement response caching with appropriate TTL",
        "6. Add support for live vs pre-game odds distinction",
        "7. Handle pagination for large response sets",
        "8. Add market availability checking before requesting"
    ]

    logger.info("Odds API integration improvements needed:")
    for improvement in improvements_needed:
        logger.info(f"  {improvement}")

    return improvements_needed


def _normalize_anytime_td_markets(df: pd.DataFrame) -> pd.DataFrame:
    """Unify {player_anytime_td, anytime_td, player_to_score_td} → 'anytime_td' with line = 1.0."""
    if df.empty:
        return df

    # Define patterns for Anytime TD markets
    anytime_td_patterns = [
        'player_anytime_td', 'anytime_td', 'player_to_score_td',
        'player to score td', 'player to score touchdown', 'td scorer', 'touchdown scorer'
    ]

    # Create a copy to avoid modifying the original
    normalized_df = df.copy()

    # Normalize market names
    for pattern in anytime_td_patterns:
        mask = normalized_df['market'].str.contains(pattern, case=False, na=False)
        if mask.any():
            normalized_df.loc[mask, 'market'] = 'anytime_td'
            # Set line to 1.0 for binary Anytime TD markets
            normalized_df.loc[mask, 'line'] = 1.0

    return normalized_df


def _normalize_player_names(df: pd.DataFrame) -> pd.DataFrame:
    """Normalize player names using existing alias table and standard formatting."""
    if df.empty:
        return df

    alias_map = _load_alias_map()

    def normalize_name(name):
        if not name or pd.isna(name):
            return ""

        name_str = str(name).strip()

        # Apply alias mapping first
        if name_str in alias_map:
            return alias_map[name_str]

        # Basic normalization: lowercase, strip punctuation, collapse spaces
        import re
        normalized = re.sub(r'[^\w\s]', '', name_str.lower())
        normalized = " ".join(normalized.split())

        return normalized

    df = df.copy()
    df['player'] = df['player'].apply(normalize_name)

    return df


def implied_prob_from_american(odds: int) -> float:
    """Convert American odds to implied probability."""
    if odds > 0:
        return 100 / (odds + 100)
    else:
        return abs(odds) / (abs(odds) + 100)


def _print_audit_summary(df: pd.DataFrame) -> None:
    """Print audit summary of fetched props data."""
    if df.empty:
        print("No props data to audit")
        return

    total_rows = len(df)
    distinct_players = df['player'].nunique()

    print(f"\n=== Props Audit Summary ===")
    print(f"Total rows: {total_rows:,}")
    print(f"Distinct players covered: {distinct_players:,}")

    # Rows per market
    market_counts = df['market'].value_counts()
    print(f"\nRows per market:")
    for market, count in market_counts.head(10).items():
        print(f"  {market}: {count:,}")

    if len(market_counts) > 10:
        print(f"  ... and {len(market_counts) - 10} more markets")

    # Books coverage
    book_counts = df['book'].value_counts()
    print(f"\nTop books by coverage:")
    for book, count in book_counts.head(5).items():
        print(f"  {book}: {count:,}")

    print("=" * 30)


def _load_alias_map() -> Dict[str, str]:
    """Load player alias map for name normalization."""
    alias_path = "config/player_aliases.csv"
    if os.path.exists(alias_path):
        try:
            alias_df = pd.read_csv(alias_path)
            return dict(zip(alias_df['alias'], alias_df['canonical']))
        except Exception as e:
            logger.warning(f"Error loading alias map: {e}")
    return {}


def _normalize_player_name(name: str, alias_map: Dict[str, str]) -> str:
    """Normalize player name using alias map and standard formatting."""
    if not name or pd.isna(name):
        return ""

    name_str = str(name).strip()

    # Apply alias mapping
    if name_str in alias_map:
        return alias_map[name_str]

    # Basic normalization: lowercase, remove extra spaces
    normalized = " ".join(name_str.lower().split())

    return normalized


def merge_props_expected(df: pd.DataFrame, props_df: pd.DataFrame) -> pd.DataFrame:
    """Merge props-derived expected stats and compute expected fantasy points.

    Joins props to DK/FC rows via (team, normalized name) with alias fallback.
    For each player, compute expected fantasy points from props using DK scoring.

    Args:
        df: DK/FC DataFrame with player data
        props_df: Props DataFrame from fetch_props_for_week

    Returns:
        DataFrame with added AnytimeTD_Prob, AnytimeTD_FP, PropsCoverage columns
    """
    if df.empty or props_df.empty:
        # Add empty columns if no props data
        result = df.copy()
        result['AnytimeTD_Prob'] = 0.0
        result['AnytimeTD_FP'] = 0.0
        result['PropsCoverage'] = 0
        return result

    # Load alias map for name normalization
    alias_map = _load_alias_map()

    # Normalize player names in both dataframes
    df_normalized = df.copy()

    # Get player name column
    player_col = None
    for col in ['Name', 'Player']:
        if col in df_normalized.columns:
            player_col = col
            break

    if not player_col:
        logger.warning("No player name column found in DataFrame")
        result = df.copy()
        result['AnytimeTD_Prob'] = 0.0
        result['AnytimeTD_FP'] = 0.0
        result['PropsCoverage'] = 0
        return result

    df_normalized['name_norm'] = df_normalized[player_col].apply(
        lambda x: _normalize_player_name(x, alias_map)
    )

    # Normalize team names for merging
    team_col = 'Team' if 'Team' in df_normalized.columns else None
    if team_col:
        df_normalized['team_norm'] = df_normalized[team_col].str.upper().str.strip()

    # Process props data
    props_processed = props_df.copy()
    props_processed['name_norm'] = props_processed['player'].apply(
        lambda x: _normalize_player_name(x, alias_map)
    )

    # Convert full team names to abbreviations for consistent matching
    team_full_to_abbrev = {
        'Minnesota Vikings': 'MIN',
        'Pittsburgh Steelers': 'PIT',
        'Detroit Lions': 'DET',
        'Cleveland Browns': 'CLE',
        'New York Giants': 'NYG',
        'Los Angeles Chargers': 'LAC',
        'Atlanta Falcons': 'ATL',
        'Washington Commanders': 'WAS',
        'Kansas City Chiefs': 'KC',
        'Baltimore Ravens': 'BAL',
        'Las Vegas Raiders': 'LV',
        'Chicago Bears': 'CHI',
        'Dallas Cowboys': 'DAL',
        'Green Bay Packers': 'GB',
        'Miami Dolphins': 'MIA',
        'New York Jets': 'NYJ',
        'Denver Broncos': 'DEN',
        'Cincinnati Bengals': 'CIN'
    }

    props_processed['team_norm'] = props_processed['team'].map(
        lambda x: team_full_to_abbrev.get(x, x)
    ).str.upper().str.strip()

    # Calculate expected fantasy points from props
    expected_fp = _calculate_expected_fp_from_props(props_processed)

    # Calculate Anytime TD metrics
    anytime_td_metrics = _calculate_anytime_td_metrics_from_props(props_processed)

    # Merge with main DataFrame
    merge_on = ['name_norm']
    if team_col and 'team_norm' in props_processed.columns:
        merge_on.append('team_norm')

    # Merge expected FP
    merged = df_normalized.merge(
        expected_fp,
        on=merge_on,
        how='left',
        suffixes=('', '_props')
    )

    # Merge Anytime TD metrics
    merged = merged.merge(
        anytime_td_metrics,
        on=merge_on,
        how='left',
        suffixes=('', '_td')
    )

    # Fill missing values
    merged['expected_fp'] = merged['expected_fp'].fillna(0.0)
    merged['AnytimeTD_Prob'] = merged['AnytimeTD_Prob'].fillna(0.0)
    merged['AnytimeTD_FP'] = merged['AnytimeTD_FP'].fillna(0.0)

    # Add PropsCoverage flag
    merged['PropsCoverage'] = ((merged['expected_fp'] > 0) | (merged['AnytimeTD_FP'] > 0)).astype(int)

    # Clean up temporary columns
    cols_to_drop = ['name_norm', 'team_norm']
    merged = merged.drop(columns=[col for col in cols_to_drop if col in merged.columns])

    logger.info(f"Merged props for {merged['PropsCoverage'].sum()} players")
    return merged


def _calculate_expected_fp_from_props(props_df: pd.DataFrame) -> pd.DataFrame:
    """Convert props to expected DK fantasy points using official DK scoring.

    DK Scoring:
    - QB: 0.04 * pass_yds + 4 * pass_tds - 1 * ints + 0.1 * rush_yds + 6 * rush_tds
    - RB/WR/TE: 0.1 * rec_yds + 1 * rec + 6 * rec_tds + 0.1 * rush_yds + 6 * rush_tds
    """
    if props_df.empty:
        return pd.DataFrame(columns=['name_norm', 'team_norm', 'expected_fp'])

    # Pivot props to get one row per player with all markets as columns
    pivot_df = props_df.pivot_table(
        index=['name_norm', 'team_norm'],
        columns='market',
        values='line',
        aggfunc='median'
    ).reset_index()

    # Calculate expected fantasy points
    expected_fp = pd.Series(0.0, index=pivot_df.index)

    # Passing stats (primarily for QBs)
    if 'player_pass_yds' in pivot_df.columns:
        expected_fp += 0.04 * pivot_df['player_pass_yds'].fillna(0.0)
    if 'player_pass_tds' in pivot_df.columns:
        expected_fp += 4.0 * pivot_df['player_pass_tds'].fillna(0.0)
    if 'player_interceptions' in pivot_df.columns:
        expected_fp -= 1.0 * pivot_df['player_interceptions'].fillna(0.0)

    # Receiving stats (for RB/WR/TE)
    if 'player_receiving_yds' in pivot_df.columns:
        expected_fp += 0.1 * pivot_df['player_receiving_yds'].fillna(0.0)
    if 'player_receptions' in pivot_df.columns:
        expected_fp += 1.0 * pivot_df['player_receptions'].fillna(0.0)
    if 'player_receiving_tds' in pivot_df.columns:
        expected_fp += 6.0 * pivot_df['player_receiving_tds'].fillna(0.0)

    # Rushing stats (for RB/WR/TE and QB)
    if 'player_rush_yds' in pivot_df.columns:
        expected_fp += 0.1 * pivot_df['player_rush_yds'].fillna(0.0)
    if 'player_rush_tds' in pivot_df.columns:
        expected_fp += 6.0 * pivot_df['player_rush_tds'].fillna(0.0)

    # Create result DataFrame
    result = pivot_df[['name_norm', 'team_norm']].copy()
    result['expected_fp'] = expected_fp

    return result


def _calculate_anytime_td_metrics_from_props(props_df: pd.DataFrame) -> pd.DataFrame:
    """Calculate Anytime TD probability and expected fantasy points from props."""
    if props_df.empty:
        return pd.DataFrame(columns=['name_norm', 'team_norm', 'AnytimeTD_Prob', 'AnytimeTD_FP'])

    # Filter for Anytime TD markets
    anytime_td_props = props_df[props_df['market'] == 'anytime_td'].copy()

    if anytime_td_props.empty:
        # Return empty metrics for all players
        unique_players = props_df[['name_norm', 'team_norm']].drop_duplicates()
        unique_players['AnytimeTD_Prob'] = 0.0
        unique_players['AnytimeTD_FP'] = 0.0
        return unique_players

    # Group by player and calculate best odds
    player_td_metrics = anytime_td_props.groupby(['name_norm', 'team_norm']).agg({
        'over_odds': 'first',
        'under_odds': 'first'
    }).reset_index()

    # Calculate implied probability from over_odds
    def calc_td_prob(over_odds):
        if pd.isna(over_odds):
            return 0.0
        try:
            return implied_prob_from_american(int(over_odds))
        except (ValueError, TypeError):
            return 0.0

    player_td_metrics['AnytimeTD_Prob'] = player_td_metrics['over_odds'].apply(calc_td_prob)

    # Calculate expected fantasy points (6.0 FP per TD)
    player_td_metrics['AnytimeTD_FP'] = player_td_metrics['AnytimeTD_Prob'] * 6.0

    # Ensure all players from props are included
    all_players = props_df[['name_norm', 'team_norm']].drop_duplicates()
    result = all_players.merge(
        player_td_metrics[['name_norm', 'team_norm', 'AnytimeTD_Prob', 'AnytimeTD_FP']],
        on=['name_norm', 'team_norm'],
        how='left'
    )

    # Fill missing values
    result['AnytimeTD_Prob'] = result['AnytimeTD_Prob'].fillna(0.0)
    result['AnytimeTD_FP'] = result['AnytimeTD_FP'].fillna(0.0)

    return result


def _normalize_anytime_td_markets(props_df: pd.DataFrame) -> pd.DataFrame:
    """Normalize Anytime TD market names to 'anytime_td'."""
    if props_df.empty:
        return props_df
    
    # Define patterns for Anytime TD markets
    anytime_td_patterns = [
        'anytime_td', 'player_anytime_td', 'player to score td', 
        'player to score touchdown', 'td scorer', 'touchdown scorer'
    ]
    
    # Create a copy to avoid modifying the original
    normalized_df = props_df.copy()
    
    # Normalize market names
    for pattern in anytime_td_patterns:
        mask = normalized_df['market'].str.contains(pattern, case=False, na=False)
        if mask.any():
            normalized_df.loc[mask, 'market'] = 'anytime_td'
            # Set line to 1.0 for binary Anytime TD markets
            normalized_df.loc[mask, 'line'] = 1.0
    
    return normalized_df


def _get_market_shift(market: str) -> float:
    """Get market-specific shift parameter for vig-adjusted mean calculation."""
    market_shifts = {
        'rec_yds': 8.0,
        'rush_yds': 8.0,
        'pass_yds': 18.0,
        'receptions': 0.5,
        'pass_tds': 0.5,
        'rush_tds': 0.5,
        'rec_tds': 0.5,
        'pass_att': 1.0,
        'rush_att': 1.0,
        'targets': 1.0,
        'comp': 1.0,
        'pass_ints': 0.2,
        'longest_rec': 4.0,
        'longest_rush': 4.0
    }
    return market_shifts.get(market, 1.0)


def impute_expected_from_props(props_df: pd.DataFrame) -> pd.DataFrame:
    """Convert props data to expected stats using vig-adjusted means.
    
    For each (player, market), convert over/under odds to implied mean.
    Returns DataFrame with expected stats columns.
    """
    if props_df.empty:
        return pd.DataFrame()
    
    try:
        # Pivot to get one row per player with all their markets
        expected_stats = props_df.pivot_table(
            index=['player', 'team'],
            columns='market',
            values='vig_adjusted_mean',
            aggfunc='first'
        ).reset_index()
        
        # Rename columns to expected stats format
        column_mapping = {
            'pass_yds': 'Exp_PassYds',
            'pass_tds': 'Exp_PassTD',
            'pass_att': 'Exp_PassAtt',
            'rush_yds': 'Exp_RushYds',
            'rush_att': 'Exp_RushAtt',
            'rush_tds': 'Exp_RushTD',
            'rec_yds': 'Exp_RecYds',
            'receptions': 'Exp_Receptions',
            'rec_tds': 'Exp_RecTD',
            'targets': 'Exp_Targets',
            'comp': 'Exp_Comp',
            'pass_ints': 'Exp_Int',
            'longest_rec': 'Exp_LongestRec',
            'longest_rush': 'Exp_LongestRush'
        }
        
        # Rename columns that exist in the data
        for old_col, new_col in column_mapping.items():
            if old_col in expected_stats.columns:
                expected_stats = expected_stats.rename(columns={old_col: new_col})
        
        # Fill missing values with 0 for expected stats
        for col in column_mapping.values():
            if col not in expected_stats.columns:
                expected_stats[col] = 0.0
            else:
                expected_stats[col] = expected_stats[col].fillna(0.0)
        
        # Calculate Anytime TD probability and expected fantasy points
        expected_stats = _calculate_anytime_td_metrics(props_df, expected_stats)
        
        logger.info(f"Imputed expected stats for {len(expected_stats)} players")
        return expected_stats
        
    except Exception as e:
        logger.error(f"Error imputing expected stats from props: {e}")
        return pd.DataFrame()


def _calculate_anytime_td_metrics(props_df: pd.DataFrame, expected_stats: pd.DataFrame) -> pd.DataFrame:
    """Calculate Anytime TD probability and expected fantasy points."""
    if props_df.empty or expected_stats.empty:
        return expected_stats
    
    # Filter for Anytime TD markets
    anytime_td_props = props_df[props_df['market'] == 'anytime_td']
    
    if anytime_td_props.empty:
        # Add empty columns if no Anytime TD data
        expected_stats['AnytimeTD_Prob'] = 0.0
        expected_stats['AnytimeTD_FP'] = 0.0
        return expected_stats
    
    # Group by player to get best Anytime TD line per player
    player_td_props = anytime_td_props.groupby(['player', 'team']).agg({
        'over_odds': 'first',
        'under_odds': 'first',
        'vig_adjusted_mean': 'median'
    }).reset_index()
    
    # Calculate implied probability from over_odds
    def calculate_implied_prob(row):
        try:
            over_odds = float(row['over_odds']) if pd.notna(row['over_odds']) else None
            if over_odds is not None:
                # Convert American odds to probability
                if over_odds > 0:
                    return 1 / (1 + 10**(-over_odds/100))
                else:
                    return 1 / (1 + 10**(over_odds/100))
            return 0.0
        except Exception:
            return 0.0
    
    player_td_props['p_anytime_td'] = player_td_props.apply(calculate_implied_prob, axis=1)
    
    # Calculate expected fantasy points (6.0 FP per TD)
    player_td_props['exp_td_fp'] = player_td_props['p_anytime_td'] * 6.0
    
    # Merge with expected_stats
    expected_stats = expected_stats.merge(
        player_td_props[['player', 'team', 'p_anytime_td', 'exp_td_fp']],
        on=['player', 'team'],
        how='left'
    )
    
    # Fill missing values
    expected_stats['p_anytime_td'] = expected_stats['p_anytime_td'].fillna(0.0)
    expected_stats['exp_td_fp'] = expected_stats['exp_td_fp'].fillna(0.0)
    
    # Rename columns to match expected output format
    expected_stats = expected_stats.rename(columns={
        'p_anytime_td': 'AnytimeTD_Prob',
        'exp_td_fp': 'AnytimeTD_FP'
    })
    
    return expected_stats


def _fuzzy_match_players(props_players: List[str], dk_players: List[str], threshold: int = 85) -> Tuple[Dict[str, str], List[str]]:
    """Perform fuzzy matching between props players and DK players."""
    alias_map = _load_alias_map()
    matches = {}
    unmatched = []

    for prop_player in props_players:
        # First try exact match with normalization
        normalized_prop = _normalize_player_name(prop_player, alias_map)
        exact_match = None

        for dk_player in dk_players:
            normalized_dk = _normalize_player_name(dk_player, alias_map)
            if normalized_prop == normalized_dk:
                exact_match = dk_player
                break

        if exact_match:
            matches[prop_player] = exact_match
            continue

        # Fallback to fuzzy matching
        best_match, score, _ = process.extractOne(prop_player, dk_players, scorer=fuzz.token_sort_ratio)
        if score >= threshold:
            matches[prop_player] = best_match
        else:
            unmatched.append(prop_player)

    return matches, unmatched


def save_unmatched_players(unmatched: List[str], season: int, week: int):
    """Save unmatched players to CSV for manual review."""
    if unmatched:
        unmatched_path = f"data/props/unmatched_players_{season}_week{week}.csv"
        os.makedirs(os.path.dirname(unmatched_path), exist_ok=True)
        unmatched_df = pd.DataFrame({'player': unmatched})
        unmatched_df.to_csv(unmatched_path, index=False)
        logger.info(f"Saved {len(unmatched)} unmatched players to {unmatched_path}")


def process_props_for_week(week: int, season: int, fetch_live: bool = False) -> pd.DataFrame:
    """Main function to process props for a given week.

    Fetches props if requested, loads and processes them, and returns expected stats.
    This function is kept for backward compatibility.
    """
    # Use the new fetch_props_for_week function
    return fetch_props_for_week(season, week, force=fetch_live)
