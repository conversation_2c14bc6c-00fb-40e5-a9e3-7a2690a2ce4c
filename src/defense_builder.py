from __future__ import annotations

import glob
import os
from typing import List, Optional

import numpy as np
import pandas as pd

from .context import SEASON_DEFAULT


DEF_DIR = os.path.join("data", "defense")
PBP_DIR = os.path.join("data", "nflfastR")


def _read_pbp_files(years: Optional[List[int]] = None) -> pd.DataFrame:
    """Read cached nflfastR PBP CSVs.

    If years is None, scan PBP_DIR for pbp_*.csv.
    """
    if years is None:
        paths = sorted(glob.glob(os.path.join(PBP_DIR, f"pbp_{SEASON_DEFAULT}.csv")))
    else:
        paths = [os.path.join(PBP_DIR, f"pbp_{y}.csv") for y in years]
        paths = [p for p in paths if os.path.exists(p)]
    if not paths:
        raise FileNotFoundError(
            f"No PBP files found. Expected cached files at {PBP_DIR}/pbp_YYYY.csv"
        )
    frames = []
    for p in paths:
        try:
            frames.append(pd.read_csv(p, low_memory=False))
        except Exception:
            pass
    if not frames:
        raise RuntimeError("Failed to read any PBP CSVs")
    # Concatenate; allow differing schemas across years
    return pd.concat(frames, ignore_index=True, sort=False)


def _ensure_numeric(s: pd.Series, default: float = 0.0) -> pd.Series:
    return pd.to_numeric(s, errors="coerce").fillna(default)


def _compute_simple_points(df: pd.DataFrame) -> dict:
    """Return dict of per-play fantasy point components for QB/RB/WR/TE approximations.

    We rely on common nflfastR columns when present; otherwise use yards_gained and touchdown.
    """
    yards = _ensure_numeric(df.get("yards_gained", pd.Series(0.0, index=df.index)))
    td_any = _ensure_numeric(df.get("touchdown", pd.Series(0.0, index=df.index))).clip(0, 1)
    complete = _ensure_numeric(df.get("complete_pass", pd.Series(0.0, index=df.index))).clip(0, 1)
    pass_attempt = _ensure_numeric(df.get("pass_attempt", pd.Series(0.0, index=df.index))).clip(0, 1)
    rush_attempt = _ensure_numeric(df.get("rush_attempt", pd.Series(0.0, index=df.index))).clip(0, 1)

    # Approx QB fantasy on pass attempts: 0.04 per yard + 4 per TD (pass TDs dominate here)
    qb_fp = 0.04 * yards * pass_attempt + 4.0 * td_any * pass_attempt

    # Approx RB fantasy on rushing plays: 0.1 per yard + 6 per TD (rush)
    rb_fp_rush = 0.1 * yards * rush_attempt + 6.0 * td_any * rush_attempt

    # Approx receiver PPR on pass plays: 1 per catch + 0.1 per yard + 6 per TD
    wrte_fp = 1.0 * complete * pass_attempt + 0.1 * yards * pass_attempt + 6.0 * td_any * pass_attempt

    return {
        "qb_fp": qb_fp,
        "rb_fp_rush": rb_fp_rush,
        "wrte_fp": wrte_fp,
        "pass_attempt": pass_attempt,
        "rush_attempt": rush_attempt,
        "yards_gained": yards,
        "touchdown": td_any,
        "sack": _ensure_numeric(df.get("sack", pd.Series(0.0, index=df.index))).clip(0, 1),
        "air_yards": _ensure_numeric(df.get("air_yards", pd.Series(np.nan, index=df.index))),
        "yardline_100": _ensure_numeric(df.get("yardline_100", pd.Series(np.nan, index=df.index))),
        "game_id": df.get("game_id", pd.Series([None]*len(df), index=df.index)).astype(str),
        "defteam": df.get("defteam", pd.Series([None]*len(df), index=df.index)).astype(str).str.upper(),
        "drive": _ensure_numeric(df.get("drive", pd.Series(np.nan, index=df.index))),
    }


def _classify_receiver_bucket(air_yards: pd.Series) -> pd.Series:
    """Heuristic: classify pass targets into RB/TE/WR buckets by air_yards.

    - RB target: air_yards <= 0
    - TE target: 0 < air_yards <= 7
    - WR target: air_yards > 7
    If air_yards is NaN, treat as RB (screens/backfield).
    """
    ay = air_yards.fillna(-1.0)
    bucket = pd.Series(["" for _ in range(len(ay))], index=ay.index)
    bucket[ay <= 0.0] = "RB"
    bucket[(ay > 0.0) & (ay <= 7.0)] = "TE"
    bucket[ay > 7.0] = "WR"
    return bucket


def _aggregate_defense(df: pd.DataFrame) -> pd.DataFrame:
    # Compute fantasy approximations & components
    comps = _compute_simple_points(df)

    # Buckets for pass targets
    rec_bucket = _classify_receiver_bucket(comps["air_yards"])  # only meaningful when pass_attempt == 1

    # Base masks
    is_pass = comps["pass_attempt"] == 1
    is_rush = comps["rush_attempt"] == 1

    # QB FP/play allowed: on pass plays
    qb_fp = comps["qb_fp"][is_pass]
    qb_plays = comps["pass_attempt"][is_pass]

    # RB FP/play allowed: rushing plays
    rb_fp = comps["rb_fp_rush"][is_rush]
    rb_plays = comps["rush_attempt"][is_rush]

    # WR/TE FP/play allowed: pass plays split by air_yards buckets
    wr_mask = is_pass & (rec_bucket == "WR")
    te_mask = is_pass & (rec_bucket == "TE")
    rb_tgt_mask = is_pass & (rec_bucket == "RB")

    wr_fp = comps["wrte_fp"][wr_mask]
    te_fp = comps["wrte_fp"][te_mask]
    rb_tgt_fp = comps["wrte_fp"][rb_tgt_mask]

    # Defensive team key
    defteam = comps["defteam"]

    # Grouped sums
    grp_qb = qb_fp.groupby(defteam[is_pass]).sum(min_count=1)
    grp_qb_plays = qb_plays.groupby(defteam[is_pass]).sum(min_count=1)

    grp_rb = rb_fp.groupby(defteam[is_rush]).sum(min_count=1)
    grp_rb_plays = rb_plays.groupby(defteam[is_rush]).sum(min_count=1)

    grp_wr = wr_fp.groupby(defteam[wr_mask]).sum(min_count=1)
    grp_wr_plays = (comps["pass_attempt"][wr_mask]).groupby(defteam[wr_mask]).sum(min_count=1)

    grp_te = te_fp.groupby(defteam[te_mask]).sum(min_count=1)
    grp_te_plays = (comps["pass_attempt"][te_mask]).groupby(defteam[te_mask]).sum(min_count=1)

    # Rates per play (avoid div by 0)
    qb_rate = (grp_qb / grp_qb_plays).replace([np.inf, -np.inf], np.nan)
    rb_rate = (grp_rb / grp_rb_plays).replace([np.inf, -np.inf], np.nan)
    wr_rate = (grp_wr / grp_wr_plays).replace([np.inf, -np.inf], np.nan)
    te_rate = (grp_te / grp_te_plays).replace([np.inf, -np.inf], np.nan)

    # Sacks per opponent pass attempt
    sack = comps["sack"]
    sack_rate = sack[is_pass].groupby(defteam[is_pass]).mean()

    # Explosive plays: >=20 yards / total plays faced
    explosive = (comps["yards_gained"] >= 20).astype(float)
    explosive_rate = explosive.groupby(defteam).mean()

    # Red-zone TD allowed: TD on plays with yardline_100 <= 20 divided by red-zone trips
    y100 = comps["yardline_100"]
    rz_mask = y100 <= 20
    td_mask = comps["touchdown"] == 1
    # Approximate trips by unique (game_id, defteam, drive) that had any rz play
    trips_df = df.loc[rz_mask, ["game_id"]].copy()
    trips_df["defteam"] = defteam[rz_mask]
    trips_df["drive"] = comps["drive"][rz_mask]
    trips = trips_df.dropna().drop_duplicates(["game_id", "defteam", "drive"]).groupby("defteam").size()

    rz_tds = df.loc[rz_mask & (td_mask == 1)].groupby(defteam[rz_mask & (td_mask == 1)]).size()
    rz_td_allowed = (rz_tds / trips).replace([np.inf, -np.inf], np.nan)

    # Combine into one frame with all teams observed
    teams = set(defteam.dropna().unique().tolist())
    out = pd.DataFrame({"Team": sorted(t for t in teams if isinstance(t, str) and t)})
    out = out.set_index("Team")

    out["fp_per_play_qb"] = qb_rate
    out["fp_per_play_rb"] = rb_rate
    out["fp_per_play_wr"] = wr_rate
    out["fp_per_play_te"] = te_rate
    out["sack_rate"] = sack_rate
    out["explosive_allowed"] = explosive_rate
    out["rz_td_allowed"] = rz_td_allowed

    # Fill NaNs with league means later
    return out


def _normalize(out: pd.DataFrame) -> pd.DataFrame:
    # dvoa-like multipliers relative to league average 1.0 (higher = worse)
    for col_raw, col_mul in [
        ("fp_per_play_qb", "dvoa_vs_qb"),
        ("fp_per_play_rb", "dvoa_vs_rb"),
        ("fp_per_play_wr", "dvoa_vs_wr"),
        ("fp_per_play_te", "dvoa_vs_te"),
    ]:
        mean_val = float(pd.to_numeric(out[col_raw], errors="coerce").mean(skipna=True)) if col_raw in out.columns else np.nan
        if not np.isfinite(mean_val) or mean_val == 0:
            out[col_mul] = 1.0
        else:
            out[col_mul] = (out[col_raw] / mean_val).astype(float)
    # Rates: fill NaNs with league mean, keep as raw rates (0..1)
    for rate_col in ("sack_rate", "rz_td_allowed", "explosive_allowed"):
        if rate_col in out.columns:
            m = float(pd.to_numeric(out[rate_col], errors="coerce").mean(skipna=True))
            out[rate_col] = pd.to_numeric(out[rate_col], errors="coerce").fillna(m if np.isfinite(m) else 0.0)
        else:
            out[rate_col] = 0.0
    # Select final columns and reset index
    final_cols = [
        "dvoa_vs_qb", "dvoa_vs_rb", "dvoa_vs_wr", "dvoa_vs_te",
        "sack_rate", "rz_td_allowed", "explosive_allowed",
    ]
    return out[final_cols].reset_index()


def build_defense_csv(week: int, season: int = SEASON_DEFAULT, weeks_back: int = 4, years: Optional[List[int]] = None) -> str:
    """Build data/defense/defense_week{week}.csv from cached nflfastR PBP."""
    os.makedirs(DEF_DIR, exist_ok=True)

    # Use season-specific PBP data
    if years is None:
        years = [season]
    
    df = _read_pbp_files(years)

    # Filter to last N weeks up to week-1 (no current week) within the specified season
    if "week" in df.columns:
        wk = pd.to_numeric(df["week"], errors="coerce")
        lo = max(1, int(week) - int(weeks_back) + 1)  # e.g., for week 4 with weeks_back=4: weeks 1-4
        hi = int(week)
        mask = (wk >= lo) & (wk <= hi)
        df = df[mask]
    # Keep only columns we need (keep extra if missing)
    keep = [
        "defteam", "pass_attempt", "rush_attempt", "air_yards", "yards_gained", "touchdown",
        "sack", "passer_player_id", "rusher_player_id", "receiver_player_id",
        "complete_pass", "yardline_100", "game_id", "drive", "week",
    ]
    keep = [c for c in keep if c in df.columns]
    df = df[keep]

    agg = _aggregate_defense(df)
    out = _normalize(agg)

    out_path = os.path.join(DEF_DIR, f"defense_week{int(week)}.csv")
    out.to_csv(out_path, index=False)
    return out_path
