from __future__ import annotations

import os
import pandas as pd
from typing import Optional
import logging

logger = logging.getLogger(__name__)


def attach_spread_total(df: pd.DataFrame, week: int, season: int) -> pd.DataFrame:
    """Attach game context (spread, total, kickoff) to player rows.
    
    Reads cached file data/odds/week{week}_spreads_totals.csv with columns:
    game_id, home_team, away_team, spread_home, total, kickoff_utc
    
    Derives per-team columns merged into the DK player rows:
    - Spread (team perspective; negative means favored)
    - Total (game total)
    - KickoffUTC (ISO string)
    
    Returns DataFrame with additional columns if data available.
    """
    file_path = f"data/odds/week{week}_spreads_totals.csv"
    
    if not os.path.exists(file_path):
        logger.info(f"Spread/total file not found: {file_path}")
        return df
    
    try:
        odds_df = pd.read_csv(file_path)
        required_cols = ['game_id', 'home_team', 'away_team', 'spread_home', 'total', 'kickoff_utc']
        if not all(col in odds_df.columns for col in required_cols):
            logger.warning(f"Spread/total file missing required columns: {required_cols}")
            return df
        
        # Create team-level data for merging
        team_data = []
        
        # Process home teams
        home_data = odds_df[['home_team', 'spread_home', 'total', 'kickoff_utc']].copy()
        home_data['team'] = home_data['home_team']
        home_data['spread'] = home_data['spread_home']  # From home perspective
        home_data['is_home'] = True
        team_data.append(home_data[['team', 'spread', 'total', 'kickoff_utc', 'is_home']])
        
        # Process away teams
        away_data = odds_df[['away_team', 'spread_home', 'total', 'kickoff_utc']].copy()
        away_data['team'] = away_data['away_team']
        away_data['spread'] = -away_data['spread_home']  # From away perspective
        away_data['is_home'] = False
        team_data.append(away_data[['team', 'spread', 'total', 'kickoff_utc', 'is_home']])
        
        team_df = pd.concat(team_data, ignore_index=True)
        
        # Merge with player data
        if 'Team' in df.columns:
            result = df.merge(
                team_df, 
                left_on='Team', 
                right_on='team', 
                how='left',
                suffixes=('', '_game')
            )
            
            # Drop the temporary team column if it was added
            if 'team' in result.columns and 'Team' in result.columns:
                result = result.drop('team', axis=1)
                
            logger.info(f"Attached spread/total data for {result['spread'].notna().sum()} players")
            return result
        else:
            logger.warning("No 'Team' column found for merging spread/total data")
            return df
            
    except Exception as e:
        logger.error(f"Error attaching spread/total data: {e}")
        return df


def attach_pace(df: pd.DataFrame, week: int, season: int) -> pd.DataFrame:
    """Attach pace data to player rows.
    
    Reads data/pace/week{week}_pace.csv (optional), columns:
    team, seconds_per_play, no_huddle_rate, situation_neutral_pace
    
    Merge Pace (seconds_per_play) with team codes. If file missing, skip and log.
    
    Returns DataFrame with additional columns if data available.
    """
    file_path = f"data/pace/week{week}_pace.csv"
    
    if not os.path.exists(file_path):
        logger.info(f"Pace file not found: {file_path}")
        return df
    
    try:
        pace_df = pd.read_csv(file_path)
        required_cols = ['team', 'seconds_per_play']
        if not all(col in pace_df.columns for col in required_cols):
            logger.warning(f"Pace file missing required columns: {required_cols}")
            return df
        
        # Merge with player data
        if 'Team' in df.columns:
            result = df.merge(
                pace_df, 
                left_on='Team', 
                right_on='team', 
                how='left',
                suffixes=('', '_pace')
            )
            
            # Drop the temporary team column if it was added
            if 'team' in result.columns and 'Team' in result.columns:
                result = result.drop('team', axis=1)
                
            logger.info(f"Attached pace data for {result['seconds_per_play'].notna().sum()} players")
            return result
        else:
            logger.warning("No 'Team' column found for merging pace data")
            return df
            
    except Exception as e:
        logger.error(f"Error attaching pace data: {e}")
        return df


def attach_game_context(df: pd.DataFrame, week: int, season: int) -> pd.DataFrame:
    """Convenience function to attach all game context data.
    
    Returns DataFrame with spread, total, kickoff, and pace data if available.
    """
    result = df.copy()
    
    # Attach spread and total first
    result = attach_spread_total(result, week, season)
    
    # Then attach pace data
    result = attach_pace(result, week, season)
    
    return result
