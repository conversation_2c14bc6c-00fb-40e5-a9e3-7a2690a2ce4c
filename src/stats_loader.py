from __future__ import annotations

import glob
import os
from typing import Dict, Optional

import pandas as pd

from .dk_nfl import _simple_name_norm as _name_norm

STATS_DIR = os.path.abspath(os.path.join(os.getcwd(), "data", "nfl_stats"))


def _read_csv_if_exists(path: str) -> Optional[pd.DataFrame]:
    try:
        if os.path.exists(path):
            return pd.read_csv(path)
    except Exception:
        pass
    return None


def load_weekly_usage(week: int) -> pd.DataFrame:
    """Load cached weekly usage CSVs from data/nfl_stats/ for a given week.

    Expected files (optional, any subset accepted):
      - week{week}_rush_attempts.csv (columns: ID?, Name?, rush_att)
      - week{week}_targets.csv (columns: ID?, Name?, targets, target_share)
      - week{week}_snap_pct.csv (columns: ID?, Name?, snap_pct)
      - week{week}_routes.csv (columns: ID?, Name?, routes)

    Returns a merged frame on best-available key with columns normalized.
    Missing files are tolerated; returns empty DataFrame if none present.
    """
    os.makedirs(STATS_DIR, exist_ok=True)

    files = {
        "rush": os.path.join(STATS_DIR, f"week{week}_rush_attempts.csv"),
        "tgt": os.path.join(STATS_DIR, f"week{week}_targets.csv"),
        "snap": os.path.join(STATS_DIR, f"week{week}_snap_pct.csv"),
        "routes": os.path.join(STATS_DIR, f"week{week}_routes.csv"),
    }

    frames: Dict[str, pd.DataFrame] = {}
    for key, path in files.items():
        df = _read_csv_if_exists(path)
        if df is None:
            continue
        # Normalize common identifier columns
        cols = {c.lower(): c for c in df.columns}
        # Build name_norm if Name present
        if "name" in cols:
            c = cols["name"]
            df["name_norm"] = df[c].map(_name_norm)
        # Accept ID column in various casings
        for cand in ("ID", "id", "player_id"):
            if cand in df.columns:
                df = df.rename(columns={cand: "ID"})
                break
        frames[key] = df

    if not frames:
        return pd.DataFrame()

    # Merge all frames on available keys (ID preferred, else name_norm)
    keys = ["ID", "name_norm"]
    # Start with the first available frame
    it = iter(frames.values())
    out = next(it)
    for df in it:
        join_keys = [k for k in keys if k in out.columns and k in df.columns]
        if not join_keys:
            # No shared join key; skip
            continue
        out = out.merge(df, on=join_keys, how="outer", suffixes=("", "_dup"))
        # Drop exact duplicate cols produced by suffixes
        dup_cols = [c for c in out.columns if c.endswith("_dup")] 
        if dup_cols:
            out = out.drop(columns=dup_cols)

    return out


def merge_usage_into_dk(dk_df: pd.DataFrame, week: Optional[int]) -> pd.DataFrame:
    """Merge usage stats into the DK dataframe by ID or normalized name.

    This does not change the DK schema used for export; caller can keep only
    original DK columns plus their own computed columns when writing.
    """
    if week is None:
        return dk_df

    usage = load_weekly_usage(week)
    if usage.empty:
        return dk_df

    out = dk_df.copy()
    # Prepare join keys on DK
    if "ID" in out.columns:
        pass  # already good
    # DK name column can be either Name or Player; name_norm was added in parser
    join_keys = []
    if "ID" in out.columns and "ID" in usage.columns:
        join_keys = ["ID"]
    elif "name_norm" in out.columns and "name_norm" in usage.columns:
        join_keys = ["name_norm"]

    if not join_keys:
        return dk_df

    out = out.merge(usage, on=join_keys, how="left")
    return out

