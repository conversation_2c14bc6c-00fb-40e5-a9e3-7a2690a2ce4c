from __future__ import annotations

import os
from typing import Iterable, List

import pandas as pd

from .context import SEASON_DEFAULT

try:  # Optional dependency
    import nfl_data_py as nfl  # type: ignore
except Exception:  # pragma: no cover
    nfl = None  # type: ignore


def load_pbp_data(years: Iterable[int] = (SEASON_DEFAULT,), cache_dir: str = "data/nflfastR") -> List[str]:
    """Download & cache nflfastR play-by-play data per season.

    Strategy:
    - Prefer nfl_data_py.import_pbp_data when available
    - Fallback to downloading the CSV.GZ from nflfastR GitHub (same data)
    - Save each season to {cache_dir}/pbp_{year}.csv

    Returns a list of cached file paths in the same order as 'years'.
    """
    os.makedirs(cache_dir, exist_ok=True)

    local_files: List[str] = []
    for yr in years:
        year = int(yr)
        path = os.path.join(cache_dir, f"pbp_{year}.csv")
        if not os.path.exists(path):
            try:
                if nfl is None:
                    raise ImportError("nfl_data_py not available")
                df = nfl.import_pbp_data([year])  # type: ignore[attr-defined]
                if not isinstance(df, pd.DataFrame):
                    raise RuntimeError("nfl_data_py.import_pbp_data returned non-DataFrame")
                df.to_csv(path, index=False)
            except Exception:
                # Fallback to GitHub raw CSV.GZ (same dataset)
                url = f"https://github.com/nflverse/nflfastR-data/blob/master/data/play_by_play_{year}.csv.gz?raw=true"
                df = pd.read_csv(url, compression="gzip")
                df.to_csv(path, index=False)
        local_files.append(path)

    return local_files
