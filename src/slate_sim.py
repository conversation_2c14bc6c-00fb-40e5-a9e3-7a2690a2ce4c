from __future__ import annotations

from dataclasses import dataclass
from typing import Dict, Iterable, List, Optional, Tuple

import numpy as np
import pandas as pd


_CAP = 50000
_POS_SLOTS = {
    "QB": 1,
    "RB": 2,
    "WR": 3,
    "TE": 1,
    "DST": 1,
}
# FLEX is one of RB/WR/TE


def _find_col(df: pd.DataFrame, opts: Iterable[str]) -> Optional[str]:
    for c in opts:
        if c in df.columns:
            return c
    return None


def _pos_series(df: pd.DataFrame) -> pd.Series:
    c = _find_col(df, ("Position", "Roster Position", "Pos", "pos"))
    if c is None:
        return pd.Series(["" for _ in range(len(df))], index=df.index)
    # take first if multi-eligible (e.g., WR/TE)
    return df[c].astype(str).str.split('/').str[0].str.upper()


def _name_series(df: pd.DataFrame) -> pd.Series:
    c = _find_col(df, ("Player", "Name"))
    if c is None:
        return pd.Series(["" for _ in range(len(df))], index=df.index)
    return df[c].astype(str)


def _team_series(df: pd.DataFrame) -> pd.Series:
    c = _find_col(df, ("Team", "TeamAbbrev", "team"))
    if c is None:
        return pd.Series(["" for _ in range(len(df))], index=df.index)
    return df[c].astype(str).str.upper()


def _opp_series(df: pd.DataFrame) -> pd.Series:
    c = _find_col(df, ("Opponent", "Opp", "opp"))
    if c is None:
        return pd.Series(["" for _ in range(len(df))], index=df.index)
    return df[c].astype(str).str.upper()


def _salary_series(df: pd.DataFrame) -> pd.Series:
    c = _find_col(df, ("Salary", "salary"))
    s = pd.to_numeric(df[c], errors="coerce") if c else pd.Series([0]*len(df), index=df.index)
    return s.fillna(0).astype(int)


@dataclass
class SlateSimResult:
    df: pd.DataFrame
    n_sims: int


def _sample_scores(rng: np.random.Generator, floors: np.ndarray, means: np.ndarray, ceils: np.ndarray) -> np.ndarray:
    """Sample one score per player using triangular distribution.

    If floor >= mean or mean >= ceil, fallback to normal(mean, std) clipped to [min, max].
    """
    n = floors.shape[0]
    out = np.empty(n, dtype=np.float32)

    # Conditions for triangular
    valid = (floors < means) & (means < ceils)
    if valid.any():
        out[valid] = rng.triangular(floors[valid], means[valid], ceils[valid]).astype(np.float32)

    # Fallback normal for invalid
    inv = ~valid
    if inv.any():
        mu = means[inv]
        lo = np.minimum(floors[inv], means[inv])
        hi = np.maximum(ceils[inv], means[inv])
        sigma = np.maximum(1.0, (hi - lo) / 4.0)
        s = rng.normal(mu, sigma).astype(np.float32)
        out[inv] = np.clip(s, lo, hi)

    return out


def _sorted_by(scores: np.ndarray, idxs: np.ndarray) -> np.ndarray:
    # return indices of idxs sorted by scores desc
    sub_scores = scores[idxs]
    order = np.argsort(-sub_scores, kind="mergesort")
    return idxs[order]


def _build_lineup(
    scores: np.ndarray,
    salaries: np.ndarray,
    positions: np.ndarray,
    cap: int = _CAP,
) -> np.ndarray:
    """Greedy lineup under DK NFL constraints with simple salary repair.

    Returns boolean mask of selected players (length N). If infeasible, returns empty selection.
    """
    n = scores.shape[0]
    sel = np.zeros(n, dtype=bool)

    # Indices by position
    qb_idx = np.where(positions == "QB")[0]
    rb_idx = np.where(positions == "RB")[0]
    wr_idx = np.where(positions == "WR")[0]
    te_idx = np.where(positions == "TE")[0]
    # Accept DST/DEF/D
    dst_idx = np.where((positions == "DST") | (positions == "DEF") | (positions == "D"))[0]

    # Basic feasibility check
    if len(qb_idx) < 1 or len(rb_idx) < 2 or len(wr_idx) < 3 or len(te_idx) < 1 or len(dst_idx) < 1:
        return sel

    # Initial greedy picks by score
    qb_sorted = _sorted_by(scores, qb_idx)
    rb_sorted = _sorted_by(scores, rb_idx)
    wr_sorted = _sorted_by(scores, wr_idx)
    te_sorted = _sorted_by(scores, te_idx)
    dst_sorted = _sorted_by(scores, dst_idx)

    picks: Dict[str, List[int]] = {"QB": [], "RB": [], "WR": [], "TE": [], "DST": [], "FLEX": []}

    picks["QB"].append(int(qb_sorted[0]))
    picks["RB"] += list(map(int, rb_sorted[:2]))
    picks["WR"] += list(map(int, wr_sorted[:3]))
    picks["TE"].append(int(te_sorted[0]))
    picks["DST"].append(int(dst_sorted[0]))

    # FLEX: best of remaining RB/WR/TE
    taken = set(picks["QB"] + picks["RB"] + picks["WR"] + picks["TE"] + picks["DST"])
    flex_pool = [i for i in list(rb_sorted) + list(wr_sorted) + list(te_sorted) if i not in taken]
    if not flex_pool:
        return sel
    picks["FLEX"].append(int(flex_pool[0]))

    # Convert to selection mask
    for grp in picks.values():
        for i in grp:
            sel[i] = True

    # Salary repair if over cap
    total_salary = int(salaries[sel].sum())
    if total_salary <= cap:
        return sel

    # Candidate lists per group (sorted by score desc)
    cand_lists: Dict[str, List[int]] = {
        "QB": list(map(int, qb_sorted)),
        "RB": list(map(int, rb_sorted)),
        "WR": list(map(int, wr_sorted)),
        "TE": list(map(int, te_sorted)),
        "DST": list(map(int, dst_sorted)),
        "FLEX": [i for i in list(rb_sorted) + list(wr_sorted) + list(te_sorted)],
    }

    # For quick lookups
    pos_of: Dict[int, str] = {}
    for i in picks["QB"]:
        pos_of[i] = "QB"
    for i in picks["RB"]:
        pos_of[i] = "RB"
    for i in picks["WR"]:
        pos_of[i] = "WR"
    for i in picks["TE"]:
        pos_of[i] = "TE"
    for i in picks["DST"]:
        pos_of[i] = "DST"
    for i in picks["FLEX"]:
        pos_of[i] = "FLEX"

    # Iterative swap-down to meet cap
    for _ in range(40):
        total_salary = int(salaries[sel].sum())
        if total_salary <= cap:
            break

        best_swap: Optional[Tuple[int, int, float]] = None  # (out_idx, in_idx, score_delta)
        selected_idxs = np.where(sel)[0]
        taken_set = set(selected_idxs.tolist())

        for out_idx in selected_idxs:
            grp = pos_of.get(int(out_idx), "")
            if grp == "":
                continue
            # Find next cheaper alternative not in selection
            if grp == "FLEX":
                pool = cand_lists["FLEX"]
            else:
                pool = cand_lists[grp]
            cur_sal = salaries[out_idx]
            cur_score = scores[out_idx]

            for in_idx in pool:
                if in_idx in taken_set:
                    continue
                # Ensure positional validity for FLEX
                if grp == "FLEX" and positions[in_idx] not in ("RB", "WR", "TE"):
                    continue
                # Must be cheaper to help
                if salaries[in_idx] >= cur_sal:
                    continue
                delta_sal = cur_sal - salaries[in_idx]
                delta_score = cur_score - scores[in_idx]  # score lost
                # Prefer swaps with small score loss per salary saved
                if best_swap is None or (delta_score / max(1, delta_sal)) < (best_swap[2] / max(1, salaries[best_swap[0]] - salaries[best_swap[1]])):
                    best_swap = (int(out_idx), int(in_idx), float(delta_score))
                # Early stop if very good
                if delta_sal >= (total_salary - cap) and delta_score <= 1.0:
                    best_swap = (int(out_idx), int(in_idx), float(delta_score))
                    break

        if best_swap is None:
            # Cannot fix; return current (may violate cap, but we will drop last added FLEX)
            # As a safeguard, drop the worst value player until under cap
            worst_idx = selected_idxs[np.argmin(scores[selected_idxs] / np.maximum(1, salaries[selected_idxs]))]
            sel[worst_idx] = False
            continue

        # Apply swap
        out_i, in_i, _ = best_swap
        sel[out_i] = False
        sel[in_i] = True
        pos_of.pop(out_i, None)
        pos_of[in_i] = grp

    # Final validation: enforce roster counts; if invalid, return empty mask
    if (sel & (positions == "QB")).sum() != 1:
        return np.zeros_like(sel)
    if (sel & (positions == "DST")).sum() + (sel & (positions == "DEF")).sum() + (sel & (positions == "D")).sum() != 1:
        return np.zeros_like(sel)
    if (sel & (positions == "TE")).sum() < 1:
        return np.zeros_like(sel)
    if (sel & (positions == "RB")).sum() + (sel & (positions == "WR")).sum() + (sel & (positions == "TE")).sum() < 6:
        return np.zeros_like(sel)

    return sel


def simulate_slate(proj_csv_path: str, num_sims: int = 100000, random_state: Optional[int] = None) -> SlateSimResult:
    df = pd.read_csv(proj_csv_path)

    name = _name_series(df)
    pos = _pos_series(df)
    team = _team_series(df)
    opp = _opp_series(df)
    sal = _salary_series(df)

    # Pull projection distribution
    mean = pd.to_numeric(df.get("ProjPoints", 0.0), errors="coerce").fillna(0.0).to_numpy(dtype=np.float32)
    floor = pd.to_numeric(df.get("Floor", 0.0), errors="coerce").fillna(0.0).to_numpy(dtype=np.float32)
    ceil = pd.to_numeric(df.get("Ceiling", 0.0), errors="coerce").fillna(0.0).to_numpy(dtype=np.float32)

    n = len(df)
    rng = np.random.default_rng(random_state)

    # Accumulators
    sum_scores = np.zeros(n, dtype=np.float64)
    boom = np.zeros(n, dtype=np.int64)
    bust = np.zeros(n, dtype=np.int64)
    opt = np.zeros(n, dtype=np.int64)

    positions = pos.to_numpy()
    teams = team.to_numpy()
    opponents = opp.to_numpy()
    salaries = sal.to_numpy()

    for _ in range(int(num_sims)):
        scores = _sample_scores(rng, floor, mean, ceil)
        sum_scores += scores
        boom += (scores >= ceil).astype(np.int64)
        bust += (scores <= floor).astype(np.int64)

        sel_mask = _build_lineup(scores, salaries, positions, cap=_CAP)
        if sel_mask.any():
            opt += sel_mask.astype(np.int64)

    # Build output
    sim_mean = (sum_scores / max(1, num_sims)).astype(float)
    opt_pct = (opt / max(1, num_sims) * 100.0).astype(float)
    boom_pct = (boom / max(1, num_sims) * 100.0).astype(float)
    bust_pct = (bust / max(1, num_sims) * 100.0).astype(float)

    # Suggested stacks (simple, rule-based)
    suggested = []
    for p in positions:
        if p == "QB":
            suggested.append("QB+WR1")
        elif p == "RB":
            suggested.append("RB+DEF")
        elif p == "WR":
            suggested.append("QB+WR")
        elif p == "TE":
            suggested.append("QB+TE")
        else:
            suggested.append("RB+DEF")

    out = pd.DataFrame({
        "Player": name,
        "Team": team,
        "Opponent": opp,
        "Salary": sal,
        "Position": pos,
        "ProjPoints": pd.to_numeric(df.get("ProjPoints", 0.0), errors="coerce").fillna(0.0),
        "Floor": pd.to_numeric(df.get("Floor", 0.0), errors="coerce").fillna(0.0),
        "Ceiling": pd.to_numeric(df.get("Ceiling", 0.0), errors="coerce").fillna(0.0),
        "SimMean": sim_mean,
        "Opt%": opt_pct,
        "Boom%": boom_pct,
        "Bust%": bust_pct,
        "Suggested_Stack": suggested,
    })

    return SlateSimResult(df=out, n_sims=int(num_sims))

