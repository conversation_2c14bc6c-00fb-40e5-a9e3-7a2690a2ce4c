from __future__ import annotations

import argparse
import os
from typing import Tuple

import pandas as pd

from .context import SEASON_DEFAULT
from .dk_nfl import read_dk_nfl_csv, discover_week_csv
from .projections_nfl import attach_projections
from .slate_sim import simulate_slate



def _export_path(week: int) -> str:
    os.makedirs("data", exist_ok=True)
    os.makedirs("data/outputs", exist_ok=True)
    os.makedirs("data/reports", exist_ok=True)
    # As requested, write to data/projections_nfl_week{week}.csv (not in outputs)
    return f"data/projections_nfl_week{week}.csv"


def _summarize(df_in: pd.DataFrame, df_out: pd.DataFrame) -> Tuple[int, int]:
    return len(df_in), len(df_out)


def _fallback_report(out_df: pd.DataFrame, week: int, season: int = 2025) -> pd.DataFrame:
    parts = []
    for col in ("BaselineSource", "vegas_meta", "defense_meta", "usage_meta"):
        if col in out_df.columns:
            counts = out_df[col].fillna("<NA>").value_counts().reset_index()
            counts.columns = ["key", "count"]
            counts.insert(0, "category", col)
            parts.append(counts)
    # Add season information
    season_df = pd.DataFrame({
        "category": ["season"],
        "key": [str(season)],
        "count": [len(out_df)]
    })
    parts.append(season_df)
    return pd.concat(parts, ignore_index=True) if parts else pd.DataFrame(columns=["category","key","count"])


def cmd_nfl(
    week: int,
    do_export: bool,
    *,
    season: int = SEASON_DEFAULT,
    with_usage: bool = False,
    with_vegas: bool = False,
    with_context: bool = False,
    with_props: bool = False,
    fetch_props: bool = False,
    strict: bool = False,
    require_defense: bool = False,
    require_vegas: bool = False,
    no_neutral: bool = False,
    report_fallbacks: bool = False,
) -> str:
    # Discover input path(s)
    in_path = discover_week_csv(week)
    if not os.path.exists(in_path):
        raise FileNotFoundError(
            f"Input DK NFL CSV not found for week {week}. Expected at data/ or data/cache."
        )

    # Fetch props if requested
    if fetch_props:
        from .props import process_props_for_week
        from .dst_props import fetch_team_totals_for_week, fetch_dst_props_for_week

        print(f"Fetching props for week {week}...")
        expected_df = process_props_for_week(week, season, fetch_live=True)
        if expected_df.empty:
            print("No props data fetched or available")
        else:
            print(f"Fetched props for {len(expected_df)} players")

        # Also fetch team totals and DST props
        print(f"Fetching team totals for week {week}...")
        try:
            team_totals_df = fetch_team_totals_for_week(season, week, force=True)
            if not team_totals_df.empty:
                print(f"Fetched team totals for {len(team_totals_df)} teams")
            else:
                print("No team totals data fetched")
        except Exception as e:
            print(f"Warning: Could not fetch team totals: {e}")

        print(f"Fetching DST props for week {week}...")
        try:
            dst_props_df = fetch_dst_props_for_week(season, week, force=True)
            if not dst_props_df.empty:
                print(f"Fetched DST props for {len(dst_props_df)} teams")
            else:
                print("No DST props data fetched")
        except Exception as e:
            print(f"Warning: Could not fetch DST props: {e}")

    dk = read_dk_nfl_csv(in_path)
    out_df = attach_projections(
        dk,
        week,
        season=season,
        with_usage=with_usage,
        with_vegas=with_vegas,
        with_context=with_context,
        with_props=with_props,
        strict=strict
    )

    # Validate: same columns + ProjPoints and same row count
    assert "ProjPoints" in out_df.columns, "Projection column missing"
    xin, xout = _summarize(dk, out_df)
    if xin != xout:
        raise AssertionError(f"Row count mismatch: in={xin}, out={xout}")

    # Fallback reporting & strict checks
    rep = _fallback_report(out_df, week, season=season)
    if report_fallbacks and not rep.empty:
        rep_path = os.path.join("data", "reports", f"fallbacks_{season}_week{week}.csv")
        rep.to_csv(rep_path, index=False)

    # Summary line
    def _cnt(col, key):
        return int(out_df[col].eq(key).sum()) if col in out_df.columns else 0
    summ = (
        f"Week {week} fallback summary — fc_proj: {_cnt('BaselineSource','fc_proj')}, "
        f"zero: {_cnt('BaselineSource','zero')}, "
        f"vegas_neutral: {_cnt('vegas_meta','neutral')}, defense_neutral: {_cnt('defense_meta','neutral')}, "
        f"usage_static: {_cnt('usage_meta','static_defaults')}"
    )
    print(summ)

    if strict:
        fail = False
        reasons = []

        # Check for defense neutrals
        defense_neutral_count = _cnt('defense_meta','neutral')
        if defense_neutral_count > 0:
            fail = True
            reasons.append(f"defense_neutral ({defense_neutral_count} players)")

        # Check for baseline zeros with salary >= 3000
        if 'BaselineSource' in out_df.columns and 'Salary' in out_df.columns:
            high_salary_zeros = out_df[
                (out_df['BaselineSource'] == 'zero') &
                (pd.to_numeric(out_df['Salary'], errors='coerce') >= 3000)
            ]
            if len(high_salary_zeros) > 10:
                fail = True
                reasons.append(f"baseline_zeros_high_salary ({len(high_salary_zeros)} players >= $3000)")
                # Print sample of problematic players
                sample_players = high_salary_zeros[['Name', 'Team', 'Salary', 'BaselineSource']].head(10)
                print("Sample players with zero baseline and salary >= $3000:")
                print(sample_players.to_string(index=False))

        # Additional strict checks
        if require_vegas and _cnt('vegas_meta','neutral') > 0:
            fail = True; reasons.append("vegas_neutral")
        if require_defense and _cnt('defense_meta','neutral') > 0:
            fail = True; reasons.append("defense_neutral")

        if fail:
            print(f"Strict mode FAIL — reasons: {', '.join(reasons)}")
            raise SystemExit(2)

    # No-neutral guard (stricter than strict)
    if no_neutral:
        fail = False
        reasons = []

        # Check for vegas neutrals on starters (Salary >= 3500)
        if 'vegas_meta' in out_df.columns and 'Salary' in out_df.columns:
            starter_vegas_neutrals = out_df[
                (out_df['vegas_meta'] == 'neutral') &
                (pd.to_numeric(out_df['Salary'], errors='coerce') >= 3500)
            ]
            if len(starter_vegas_neutrals) > 0:
                fail = True
                reasons.append(f"vegas_neutral_starters ({len(starter_vegas_neutrals)} players >= $3500)")

        # Check for any defense neutrals
        if _cnt('defense_meta','neutral') > 0:
            fail = True
            reasons.append(f"defense_neutral ({_cnt('defense_meta','neutral')} players)")

        # Check for props coverage on offensive players
        if 'PropsCoverage' in out_df.columns:
            pos_col = None
            for cand in ("Position", "Roster Position", "Pos", "pos"):
                if cand in out_df.columns:
                    pos_col = cand
                    break

            if pos_col:
                pos_series = out_df[pos_col].astype(str).str.upper()
                offensive_mask = (
                    pos_series.str.contains("QB", na=False) |
                    pos_series.str.contains("RB", na=False) |
                    pos_series.str.contains("WR", na=False) |
                    pos_series.str.contains("TE", na=False)
                )
                offensive_players = out_df[offensive_mask]
                uncovered_players = offensive_players[offensive_players['PropsCoverage'] == 0]

                if len(offensive_players) > 0:
                    uncovered_pct = len(uncovered_players) / len(offensive_players)
                    if uncovered_pct > 0.4:  # More than 40% uncovered
                        fail = True
                        reasons.append(f"props_coverage_low ({uncovered_pct:.1%} uncovered)")

        if fail:
            print(f"No-neutral mode FAIL — reasons: {', '.join(reasons)}")
            raise SystemExit(2)

    out_path = _export_path(week)
    if do_export:
        # Keep original column order from input, excluding helper cols, then append our metrics
        cols = [c for c in dk.columns if c not in ("name_norm", "team_norm")]
        for c in (
            "VegasPts", "vegas_meta", "UsageAdj", "usage_meta", "ProjPoints", "Ceiling", "Floor", "Ownership", "BaselineSource", "defense_meta",
            "AnytimeTD_Prob", "AnytimeTD_FP", "DST_Sacks", "DST_TO", "DST_TD", "DST_Safety", "DST_Block", "DST_PtsAllowed", "DST_ProjPoints"
        ):
            if c in out_df.columns and c not in cols:
                cols.append(c)
        out_df[cols].to_csv(out_path, index=False)
    # Optional quick preview: top 10 rows not using fc_proj
    try:
        sample_non_fc = out_df[out_df.get('BaselineSource', 'fc_proj') != 'fc_proj'].head(10)
        if not sample_non_fc.empty:
            show_cols = [c for c in [
                'Name','Player','Team','Pos','Position','Salary','BaselineSource','ProjPoints','VegasPts','vegas_meta','defense_meta','usage_meta'
            ] if c in out_df.columns]
            print("Top 10 rows where BaselineSource != 'fc_proj':")
            print(sample_non_fc[show_cols].to_string(index=False))
    except Exception:
        pass

    print(f"Processed {xin} players, exported {xout} with projections")
    return out_path


def _slate_export_path(week: int) -> str:
    os.makedirs("data", exist_ok=True)
    return f"data/slate_sim_week{week}.csv"


def cmd_slate(week: int, *, num_sims: int = 100000) -> str:
    in_path = f"data/projections_nfl_week{week}.csv"
    if not os.path.exists(in_path):
        raise FileNotFoundError(f"Projections CSV not found at {in_path}. Run nfl export first.")
    res = simulate_slate(in_path, num_sims=num_sims)
    out_path = _slate_export_path(week)
    res.df.to_csv(out_path, index=False)
    print(f"Ran {res.n_sims} simulations, exported {len(res.df)} players with Opt% to {out_path}")
    return out_path



def main():
    # Load environment variables from .env file
    from dotenv import load_dotenv
    load_dotenv()
    
    parser = argparse.ArgumentParser(prog="src.cli")
    sub = parser.add_subparsers(dest="command")

    nfl_p = sub.add_parser("nfl", help="DraftKings NFL → FC-ready CSV")
    nfl_p.add_argument("--week", type=int, required=True, help="NFL week (e.g., 4)")
    nfl_p.add_argument("--season", type=int, default=SEASON_DEFAULT, help=f"Season (default {SEASON_DEFAULT})")
    nfl_p.add_argument("--export", action="store_true", help="Write output CSV")
    nfl_p.add_argument("--with-vegas", action="store_true", help="Scale projections by team Vegas implied totals")
    nfl_p.add_argument("--with-usage", action="store_true", help="Apply role-based usage multipliers (QB/RB/WR/TE)")
    nfl_p.add_argument("--with-context", action="store_true", help="Attach game context (spread, total, pace) if files exist")
    nfl_p.add_argument("--with-props", action="store_true", help="Blend props-derived expectations with projections")
    nfl_p.add_argument("--fetch-props", action="store_true", help="Fetch props live from Odds API → cache → process")
    nfl_p.add_argument("--strict", action="store_true", help="Fail if critical fallbacks present (see --require-*)")
    nfl_p.add_argument("--require-defense", action="store_true", help="With --strict, require defense_meta!=neutral for all rows")
    nfl_p.add_argument("--require-vegas", action="store_true", help="With --strict, require vegas_meta!=neutral for all rows")
    nfl_p.add_argument("--no-neutral", action="store_true", help="Block any neutral paths - stricter than --strict")
    nfl_p.add_argument("--report-fallbacks", action="store_true", help="Write data/reports/fallbacks_week{W}.csv summary")


    slate_p = sub.add_parser("slate", help="Run slate-level Monte Carlo simulation")
    slate_p.add_argument("--week", type=int, required=True, help="NFL week (e.g., 4)")
    slate_p.add_argument("--sims", type=int, default=100000, help="Number of slate simulations (default 100000)")

    fetch_p = sub.add_parser("fetch-nflfastr", help="Download & cache nflfastR play-by-play data")
    fetch_p.add_argument("--season", type=int, default=SEASON_DEFAULT, help=f"Season (default {SEASON_DEFAULT})")

    # Build-defense subcommand
    build_def = sub.add_parser("build-defense", help="Build defense_week{W}.csv from nflfastR PBP")
    build_def.add_argument("--week", type=int, required=True, help="NFL week to build for (e.g., 4)")
    build_def.add_argument("--season", type=int, default=SEASON_DEFAULT, help=f"Season (default {SEASON_DEFAULT})")
    build_def.add_argument("--weeks-back", type=int, default=4, help="Number of weeks to look back (default 4)")

    # Props subcommand
    props_p = sub.add_parser("props", help="Fetch NFL player props from Odds API")
    props_p.add_argument("--season", type=int, default=SEASON_DEFAULT, help=f"Season (default {SEASON_DEFAULT})")
    props_p.add_argument("--week", type=int, required=True, help="NFL week (e.g., 4)")
    props_p.add_argument("--force", action="store_true", help="Force refresh of cached props data")

    args = parser.parse_args()

    if args.command == "nfl":
        cmd_nfl(
            week=args.week,
            do_export=args.export,
            season=args.season,
            with_usage=args.with_usage,
            with_vegas=args.with_vegas,
            with_context=args.with_context,
            with_props=args.with_props,
            fetch_props=args.fetch_props,
            strict=args.strict,
            require_defense=args.require_defense,
            require_vegas=args.require_vegas,
            no_neutral=args.no_neutral,
            report_fallbacks=args.report_fallbacks,
        )
    elif args.command == "slate":
        cmd_slate(week=args.week, num_sims=args.sims)
    elif args.command == "fetch-nflfastr":
        from .data_nflfastr import load_pbp_data
        files = load_pbp_data([args.season])
        print(f"✅ Cached {len(files)} seasons of PBP data: {files}")
    elif args.command == "build-defense":
        from .defense_builder import build_defense_csv
        out_path = build_defense_csv(args.week, season=args.season, weeks_back=args.weeks_back)
        print(f"✅ Built defense file: {out_path}")
    elif args.command == "props":
        from .props import fetch_props_for_week
        props_df = fetch_props_for_week(args.season, args.week, force=args.force)
        if not props_df.empty:
            print(f"Fetched {len(props_df)} props across {props_df['market'].nunique()} markets for {props_df['player'].nunique()} players (Week {args.week}, {args.season}). Saved to data/props/props_{args.season}_week{args.week}.csv")
        else:
            print("No props data fetched or available")
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
