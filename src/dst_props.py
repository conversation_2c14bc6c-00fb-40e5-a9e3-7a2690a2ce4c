from __future__ import annotations

import os
import json
import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Tuple
import logging
from datetime import datetime
import time
from tenacity import retry, stop_after_attempt, wait_exponential_jitter

logger = logging.getLogger(__name__)


def fetch_team_totals_for_week(season: int, week: int, *, force: bool = False) -> pd.DataFrame:
    """Fetch team totals (over/under) for all NFL games in a given week.
    
    Returns DataFrame with columns: team, opponent, implied_total, game_id, season, week
    """
    # Check cache first unless force refresh
    output_csv = f"data/odds/team_totals_{season}_week{week}.csv"
    if not force and os.path.exists(output_csv):
        logger.info(f"Using cached team totals from {output_csv}")
        return pd.read_csv(output_csv)

    # Require Odds API client
    try:
        from src.app.io.odds import list_events, team_totals
        from src.app.utils.normalize import normalize_team
    except ImportError as e:
        raise ImportError("Odds API client is required but not available") from e

    # Create output directories
    os.makedirs("data/odds", exist_ok=True)
    
    try:
        # Get all NFL games for the week
        logger.info(f"Fetching NFL games for {season} week {week}")
        events = list_events("americanfootball_nfl")
        if not events:
            raise ValueError("No NFL events found from Odds API")

        # Filter events for the current week
        nfl_games = []
        for event in events:
            if event.get('id') and event.get('home_team') and event.get('away_team'):
                nfl_games.append(event)

        if not nfl_games:
            raise ValueError("No valid NFL games found")

        logger.info(f"Found {len(nfl_games)} NFL games to process for team totals")

        # Fetch team totals for each game
        all_totals = []
        
        for i, game in enumerate(nfl_games):
            game_id = game['id']
            home_team = game['home_team']
            away_team = game['away_team']
            
            logger.info(f"Processing game {i+1}/{len(nfl_games)}: {away_team} @ {home_team}")
            
            try:
                # Fetch team totals with retry
                totals_data = _fetch_team_totals_with_retry(game_id, home_team, away_team)
                if totals_data:
                    all_totals.extend(totals_data)
                    
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                logger.warning(f"Failed to fetch team totals for {away_team} @ {home_team}: {e}")
                continue

        # Create DataFrame
        if all_totals:
            totals_df = pd.DataFrame(all_totals)
            totals_df['season'] = season
            totals_df['week'] = week
            totals_df['retrieved_at'] = datetime.utcnow().isoformat()
            
            # Save to CSV
            totals_df.to_csv(output_csv, index=False)
            logger.info(f"Saved {len(totals_df)} team totals to {output_csv}")
            
            return totals_df
        else:
            logger.warning("No team totals data fetched")
            return pd.DataFrame(columns=['team', 'opponent', 'implied_total', 'game_id', 'season', 'week'])

    except Exception as e:
        logger.error(f"Error in fetch_team_totals_for_week: {e}")
        raise


@retry(stop=stop_after_attempt(3), wait=wait_exponential_jitter(initial=1, max=8))
def _fetch_team_totals_with_retry(game_id: str, home_team: str, away_team: str) -> List[Dict]:
    """Fetch team totals for a single game with retry logic."""
    try:
        from src.app.io.odds import team_totals
        from src.app.utils.normalize import normalize_team
    except ImportError:
        return []

    try:
        logger.info(f"Fetching team totals for {away_team} @ {home_team}")
        
        # Call the Odds API for team totals
        totals_data = team_totals(game_id, regions="us,us2", oddsFormat="american")
        
        if not totals_data:
            logger.warning(f"No team totals data returned for game {game_id}")
            return []

        # Parse the response to extract implied totals
        game_totals = []
        
        # The API response structure varies, but typically contains bookmakers with totals markets
        if isinstance(totals_data, dict) and 'bookmakers' in totals_data:
            for bookmaker in totals_data['bookmakers']:
                if 'markets' in bookmaker:
                    for market in bookmaker['markets']:
                        if market.get('key') == 'totals':
                            # Extract the total line (usually the over/under number)
                            for outcome in market.get('outcomes', []):
                                if outcome.get('name') == 'Over':
                                    total_line = outcome.get('point', 0)
                                    break
                            else:
                                continue
                            
                            # Calculate implied totals for each team (typically total/2)
                            implied_total = total_line / 2.0
                            
                            # Add entries for both teams
                            game_totals.extend([
                                {
                                    'team': normalize_team(home_team) if 'normalize_team' in locals() else home_team,
                                    'opponent': normalize_team(away_team) if 'normalize_team' in locals() else away_team,
                                    'implied_total': implied_total,
                                    'game_total': total_line,
                                    'game_id': game_id,
                                    'book': bookmaker.get('title', 'unknown')
                                },
                                {
                                    'team': normalize_team(away_team) if 'normalize_team' in locals() else away_team,
                                    'opponent': normalize_team(home_team) if 'normalize_team' in locals() else home_team,
                                    'implied_total': implied_total,
                                    'game_total': total_line,
                                    'game_id': game_id,
                                    'book': bookmaker.get('title', 'unknown')
                                }
                            ])
                            break  # Use first available totals market
                    break  # Use first bookmaker with totals
        
        return game_totals

    except Exception as e:
        logger.error(f"Error fetching team totals for {away_team} @ {home_team}: {e}")
        return []


def fetch_dst_props_for_week(season: int, week: int, *, force: bool = False) -> pd.DataFrame:
    """Fetch defensive player props and aggregate to team DST projections.

    DST props come from individual defensive players (sacks, tackles, etc.) that need to be
    aggregated to team-level DST projections. We'll extract defensive props from the main
    props data rather than trying to fetch separate DST markets.

    Returns DataFrame with columns: team, market, line, over_odds, under_odds, book, game_id, season, week
    """
    # Check cache first unless force refresh
    output_csv = f"data/props/dst_props_{season}_week{week}.csv"
    if not force and os.path.exists(output_csv):
        logger.info(f"Using cached DST props from {output_csv}")
        return pd.read_csv(output_csv)

    # Create output directories
    os.makedirs("data/props", exist_ok=True)

    try:
        # Load the main props data which should contain defensive player props
        props_csv = f"data/props/props_{season}_week{week}.csv"
        if not os.path.exists(props_csv):
            logger.warning(f"Props CSV not found: {props_csv}. Cannot extract DST props.")
            return pd.DataFrame(columns=['team', 'market', 'line', 'over_odds', 'under_odds', 'book', 'game_id', 'season', 'week'])

        props_df = pd.read_csv(props_csv)
        if props_df.empty:
            logger.warning("Props CSV is empty. Cannot extract DST props.")
            return pd.DataFrame(columns=['team', 'market', 'line', 'over_odds', 'under_odds', 'book', 'game_id', 'season', 'week'])

        # Extract defensive props (sacks, tackles, etc.)
        defensive_markets = ['player_sacks', 'player_tackles_assists']
        dst_props = props_df[props_df['market'].isin(defensive_markets)].copy()

        if dst_props.empty:
            logger.warning("No defensive props found in main props data.")
            return pd.DataFrame(columns=['team', 'market', 'line', 'over_odds', 'under_odds', 'book', 'game_id', 'season', 'week'])

        # Aggregate defensive props by team
        # Group by team and market, sum the lines (expected values)
        team_dst_props = dst_props.groupby(['team', 'market']).agg({
            'line': 'sum',  # Sum expected values for team total
            'over_odds': 'mean',  # Average odds (could be improved)
            'under_odds': 'mean',
            'book': 'first',
            'game_id': 'first'
        }).reset_index()

        # Map player markets to DST markets
        market_mapping = {
            'player_sacks': 'team_sacks',
            'player_tackles_assists': 'team_tackles'
        }

        team_dst_props['market'] = team_dst_props['market'].map(market_mapping).fillna(team_dst_props['market'])

        # Add metadata
        team_dst_props['season'] = season
        team_dst_props['week'] = week
        team_dst_props['retrieved_at'] = datetime.utcnow().isoformat()

        # Save to CSV
        team_dst_props.to_csv(output_csv, index=False)
        logger.info(f"Saved {len(team_dst_props)} aggregated DST props to {output_csv}")

        return team_dst_props

    except Exception as e:
        logger.error(f"Error in fetch_dst_props_for_week: {e}")
        raise


def _get_defensive_player_team_mapping() -> Dict[str, str]:
    """Get a mapping of defensive players to their teams.

    This is a simplified mapping - in production you'd want to maintain
    a comprehensive roster database or fetch from an API.
    """
    # This is a basic mapping - you'd want to expand this or fetch from a roster API
    defensive_players = {
        # Example mappings - would need to be comprehensive
        'T.J. Watt': 'PIT',
        'Micah Parsons': 'DAL',
        'Myles Garrett': 'CLE',
        'Aaron Donald': 'LAR',
        'Khalil Mack': 'LAC',
        # Add more as needed...
    }
    return defensive_players


def _aggregate_defensive_props_to_dst(props_df: pd.DataFrame) -> pd.DataFrame:
    """Aggregate individual defensive player props to team DST projections."""
    if props_df.empty:
        return pd.DataFrame(columns=['team', 'market', 'line', 'over_odds', 'under_odds'])

    # Filter for defensive markets
    defensive_markets = ['player_sacks', 'player_tackles_assists']
    defensive_props = props_df[props_df['market'].isin(defensive_markets)].copy()

    if defensive_props.empty:
        return pd.DataFrame(columns=['team', 'market', 'line', 'over_odds', 'under_odds'])

    # Get team mapping for defensive players
    player_team_map = _get_defensive_player_team_mapping()

    # Try to map players to teams using the team column if available
    if 'team' in defensive_props.columns:
        # Use existing team assignments
        pass
    else:
        # Try to map using player names
        defensive_props['team'] = defensive_props['player'].map(player_team_map)
        # Drop rows where we couldn't map to a team
        defensive_props = defensive_props.dropna(subset=['team'])

    if defensive_props.empty:
        return pd.DataFrame(columns=['team', 'market', 'line', 'over_odds', 'under_odds'])

    # Aggregate by team and market
    team_aggregated = defensive_props.groupby(['team', 'market']).agg({
        'line': 'sum',  # Sum expected values for team total
        'over_odds': 'mean',  # Average odds
        'under_odds': 'mean'
    }).reset_index()

    # Map to DST market names
    market_mapping = {
        'player_sacks': 'team_sacks',
        'player_tackles_assists': 'team_tackles'
    }

    team_aggregated['market'] = team_aggregated['market'].map(market_mapping).fillna(team_aggregated['market'])

    return team_aggregated


def calculate_dst_projections(df: pd.DataFrame, season: int, week: int) -> pd.DataFrame:
    """Calculate DST projections using props and team totals.

    DraftKings DST Scoring:
    - Sacks = line × 1 pt
    - Turnovers (INT/FUM) = estimated × 2 pts (not available in props, use baseline)
    - Def/ST TD = estimated × 6 pts (not available in props, use baseline)
    - Safety = estimated × 2 pts (not available in props, use baseline)
    - Blocked Kick = estimated × 2 pts (not available in props, use baseline)
    - Points Allowed = from opponent implied total:
      0 = +10, 1–6 = +7, 7–13 = +4, 14–20 = +1, 21–27 = 0, 28–34 = -1, 35+ = -4
    """
    if df.empty:
        return df

    # Filter for DST positions
    dst_mask = df.get('Position', df.get('Pos', '')).str.upper().isin(['DST', 'DEF', 'D'])
    if not dst_mask.any():
        # Add empty DST columns for non-DST players
        for col in ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']:
            if col not in df.columns:
                df[col] = 0.0
        return df

    dst_df = df[dst_mask].copy()

    # Load team totals and DST props
    try:
        team_totals_df = fetch_team_totals_for_week(season, week, force=False)
        dst_props_df = fetch_dst_props_for_week(season, week, force=False)
    except Exception as e:
        logger.warning(f"Could not load team totals or DST props: {e}")
        # Add empty DST columns
        for col in ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']:
            if col not in dst_df.columns:
                dst_df[col] = 0.0
        # Update main dataframe
        df.loc[dst_mask, ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']] = dst_df[['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']].values
        return df

    # Get team column
    team_col = 'Team' if 'Team' in dst_df.columns else None
    if not team_col:
        logger.warning("No Team column found for DST projections")
        # Add empty DST columns
        for col in ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']:
            if col not in dst_df.columns:
                dst_df[col] = 0.0
        df.loc[dst_mask, ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']] = dst_df[['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']].values
        return df

    # Normalize team names for merging
    dst_df['team_norm'] = dst_df[team_col].astype(str).str.upper()

    # 1. Calculate Points Allowed from team totals
    if not team_totals_df.empty:
        team_totals_df['team_norm'] = team_totals_df['team'].astype(str).str.upper()

        # For DST, we need the opponent's implied total, not our own team's total
        # Get opponent column
        opp_col = 'Opponent' if 'Opponent' in dst_df.columns else 'Opp' if 'Opp' in dst_df.columns else None

        if opp_col:
            dst_df['opp_norm'] = dst_df[opp_col].astype(str).str.upper()
            # Merge opponent totals
            dst_df = dst_df.merge(
                team_totals_df[['team_norm', 'implied_total']].rename(columns={'team_norm': 'opp_norm', 'implied_total': 'opp_implied_total'}),
                on='opp_norm',
                how='left'
            )
            dst_df['DST_PtsAllowed'] = dst_df['opp_implied_total'].apply(_calculate_points_allowed_score)
        else:
            logger.warning("No opponent column found for DST points allowed calculation")
            dst_df['DST_PtsAllowed'] = 0.0
    else:
        dst_df['DST_PtsAllowed'] = 0.0

    # 2. Calculate Sacks from aggregated defensive props
    if not dst_props_df.empty:
        # Normalize team names in props
        dst_props_df['team_norm'] = dst_props_df['team'].astype(str).str.upper()

        # Pivot DST props to get one row per team
        dst_props_pivot = dst_props_df.pivot_table(
            index='team_norm',
            columns='market',
            values='line',
            aggfunc='sum'  # Sum all defensive player props for team total
        ).reset_index()

        # Merge DST props
        dst_df = dst_df.merge(
            dst_props_pivot,
            on='team_norm',
            how='left'
        )

        # Calculate DST scoring components from available props
        # Cap sacks at reasonable levels (NFL record is 12, typical range is 1-6)
        sacks_raw = dst_df.get('team_sacks', 0)
        sacks_capped = sacks_raw.clip(upper=6.0)  # Cap at 6 sacks max

        # Log any capped values for debugging
        if hasattr(sacks_raw, 'max') and sacks_raw.max() > 6.0:
            high_sack_teams = dst_df[sacks_raw > 6.0][['team_norm', 'team_sacks']] if 'team_sacks' in dst_df.columns else []
            if len(high_sack_teams) > 0:
                logger.warning(f"Capped unrealistic sack projections: {high_sack_teams.to_dict('records')}")

        dst_df['DST_Sacks'] = sacks_capped * 1.0  # 1 pt per sack

        # For markets not available in props, set to 0.0 (no data = no projection)
        dst_df['DST_TO'] = 0.0  # No turnover props available
        dst_df['DST_TD'] = 0.0  # No defensive TD props available
        dst_df['DST_Safety'] = 0.0  # No safety props available
        dst_df['DST_Block'] = 0.0  # No blocked kick props available
    else:
        # No props data available - set all to 0.0 (no data = no projection)
        dst_df['DST_Sacks'] = 0.0  # No sack props available
        dst_df['DST_TO'] = 0.0  # No turnover props available
        dst_df['DST_TD'] = 0.0  # No defensive TD props available
        dst_df['DST_Safety'] = 0.0  # No safety props available
        dst_df['DST_Block'] = 0.0  # No blocked kick props available

    # 3. Calculate total DST projection
    dst_df['DST_ProjPoints'] = (
        dst_df['DST_Sacks'] +
        dst_df['DST_TO'] +
        dst_df['DST_TD'] +
        dst_df['DST_Safety'] +
        dst_df['DST_Block'] +
        dst_df['DST_PtsAllowed']
    )

    # 4. Add empty DST columns for non-DST players
    for col in ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']:
        if col not in df.columns:
            df[col] = 0.0

    # 5. Update the main DataFrame
    df.loc[dst_mask, ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']] = dst_df[['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']].values

    return df


def _calculate_points_allowed_score(implied_total: float) -> float:
    """Calculate DraftKings points allowed scoring based on opponent implied total."""
    if pd.isna(implied_total):
        return 0.0
    
    # DraftKings points allowed scoring
    if implied_total == 0:
        return 10.0
    elif 1 <= implied_total <= 6:
        return 7.0
    elif 7 <= implied_total <= 13:
        return 4.0
    elif 14 <= implied_total <= 20:
        return 1.0
    elif 21 <= implied_total <= 27:
        return 0.0
    elif 28 <= implied_total <= 34:
        return -1.0
    else:  # 35+
        return -4.0
