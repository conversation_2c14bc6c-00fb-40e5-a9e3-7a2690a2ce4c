from __future__ import annotations

import os
import json
import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Tuple
import logging
from datetime import datetime
import time
from tenacity import retry, stop_after_attempt, wait_exponential_jitter

logger = logging.getLogger(__name__)


def fetch_team_totals_for_week(season: int, week: int, *, force: bool = False) -> pd.DataFrame:
    """Fetch team totals (over/under) for all NFL games in a given week.
    
    Returns DataFrame with columns: team, opponent, implied_total, game_id, season, week
    """
    # Check cache first unless force refresh
    output_csv = f"data/odds/team_totals_{season}_week{week}.csv"
    if not force and os.path.exists(output_csv):
        logger.info(f"Using cached team totals from {output_csv}")
        return pd.read_csv(output_csv)

    # Require Odds API client
    try:
        from src.app.io.odds import list_events, team_totals
        from src.app.utils.normalize import normalize_team
    except ImportError as e:
        raise ImportError("Odds API client is required but not available") from e

    # Create output directories
    os.makedirs("data/odds", exist_ok=True)
    
    try:
        # Get all NFL games for the week
        logger.info(f"Fetching NFL games for {season} week {week}")
        events = list_events("americanfootball_nfl")
        if not events:
            raise ValueError("No NFL events found from Odds API")

        # Filter events for the current week
        nfl_games = []
        for event in events:
            if event.get('id') and event.get('home_team') and event.get('away_team'):
                nfl_games.append(event)

        if not nfl_games:
            raise ValueError("No valid NFL games found")

        logger.info(f"Found {len(nfl_games)} NFL games to process for team totals")

        # Fetch team totals for each game
        all_totals = []
        
        for i, game in enumerate(nfl_games):
            game_id = game['id']
            home_team = game['home_team']
            away_team = game['away_team']
            
            logger.info(f"Processing game {i+1}/{len(nfl_games)}: {away_team} @ {home_team}")
            
            try:
                # Fetch team totals with retry
                totals_data = _fetch_team_totals_with_retry(game_id, home_team, away_team)
                if totals_data:
                    all_totals.extend(totals_data)
                    
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                logger.warning(f"Failed to fetch team totals for {away_team} @ {home_team}: {e}")
                continue

        # Create DataFrame
        if all_totals:
            totals_df = pd.DataFrame(all_totals)
            totals_df['season'] = season
            totals_df['week'] = week
            totals_df['retrieved_at'] = datetime.utcnow().isoformat()
            
            # Save to CSV
            totals_df.to_csv(output_csv, index=False)
            logger.info(f"Saved {len(totals_df)} team totals to {output_csv}")
            
            return totals_df
        else:
            logger.warning("No team totals data fetched")
            return pd.DataFrame(columns=['team', 'opponent', 'implied_total', 'game_id', 'season', 'week'])

    except Exception as e:
        logger.error(f"Error in fetch_team_totals_for_week: {e}")
        raise


@retry(stop=stop_after_attempt(3), wait=wait_exponential_jitter(initial=1, max=8))
def _fetch_team_totals_with_retry(game_id: str, home_team: str, away_team: str) -> List[Dict]:
    """Fetch team totals for a single game with retry logic."""
    try:
        from src.app.io.odds import team_totals
        from src.app.utils.normalize import normalize_team
    except ImportError:
        return []

    try:
        logger.info(f"Fetching team totals for {away_team} @ {home_team}")
        
        # Call the Odds API for team totals
        totals_data = team_totals(game_id, regions="us,us2", oddsFormat="american")
        
        if not totals_data:
            logger.warning(f"No team totals data returned for game {game_id}")
            return []

        # Parse the response to extract implied totals
        game_totals = []
        
        # The API response structure varies, but typically contains bookmakers with totals markets
        if isinstance(totals_data, dict) and 'bookmakers' in totals_data:
            for bookmaker in totals_data['bookmakers']:
                if 'markets' in bookmaker:
                    for market in bookmaker['markets']:
                        if market.get('key') == 'totals':
                            # Extract the total line (usually the over/under number)
                            for outcome in market.get('outcomes', []):
                                if outcome.get('name') == 'Over':
                                    total_line = outcome.get('point', 0)
                                    break
                            else:
                                continue
                            
                            # Calculate implied totals for each team (typically total/2)
                            implied_total = total_line / 2.0
                            
                            # Add entries for both teams
                            game_totals.extend([
                                {
                                    'team': normalize_team(home_team) if 'normalize_team' in locals() else home_team,
                                    'opponent': normalize_team(away_team) if 'normalize_team' in locals() else away_team,
                                    'implied_total': implied_total,
                                    'game_total': total_line,
                                    'game_id': game_id,
                                    'book': bookmaker.get('title', 'unknown')
                                },
                                {
                                    'team': normalize_team(away_team) if 'normalize_team' in locals() else away_team,
                                    'opponent': normalize_team(home_team) if 'normalize_team' in locals() else home_team,
                                    'implied_total': implied_total,
                                    'game_total': total_line,
                                    'game_id': game_id,
                                    'book': bookmaker.get('title', 'unknown')
                                }
                            ])
                            break  # Use first available totals market
                    break  # Use first bookmaker with totals
        
        return game_totals

    except Exception as e:
        logger.error(f"Error fetching team totals for {away_team} @ {home_team}: {e}")
        return []


def fetch_dst_props_for_week(season: int, week: int, *, force: bool = False) -> pd.DataFrame:
    """Fetch DST props for all NFL games in a given week.
    
    Returns DataFrame with columns: team, market, line, over_odds, under_odds, book, game_id, season, week
    """
    # Check cache first unless force refresh
    output_csv = f"data/props/dst_props_{season}_week{week}.csv"
    if not force and os.path.exists(output_csv):
        logger.info(f"Using cached DST props from {output_csv}")
        return pd.read_csv(output_csv)

    # Require Odds API client
    try:
        from src.app.io.odds import list_events, dst_props
        from src.app.utils.normalize import normalize_team
    except ImportError as e:
        raise ImportError("Odds API client is required but not available") from e

    # Create output directories
    os.makedirs("data/props", exist_ok=True)
    
    try:
        # Get all NFL games for the week
        logger.info(f"Fetching NFL games for DST props {season} week {week}")
        events = list_events("americanfootball_nfl")
        if not events:
            raise ValueError("No NFL events found from Odds API")

        # Filter events for the current week
        nfl_games = []
        for event in events:
            if event.get('id') and event.get('home_team') and event.get('away_team'):
                nfl_games.append(event)

        if not nfl_games:
            raise ValueError("No valid NFL games found")

        logger.info(f"Found {len(nfl_games)} NFL games to process for DST props")

        # Fetch DST props for each game
        all_dst_props = []
        
        for i, game in enumerate(nfl_games):
            game_id = game['id']
            home_team = game['home_team']
            away_team = game['away_team']
            
            logger.info(f"Processing DST props for game {i+1}/{len(nfl_games)}: {away_team} @ {home_team}")
            
            try:
                # Fetch DST props with retry
                dst_data = _fetch_dst_props_with_retry(game_id, home_team, away_team)
                if dst_data:
                    all_dst_props.extend(dst_data)
                    
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                logger.warning(f"Failed to fetch DST props for {away_team} @ {home_team}: {e}")
                continue

        # Create DataFrame
        if all_dst_props:
            dst_df = pd.DataFrame(all_dst_props)
            dst_df['season'] = season
            dst_df['week'] = week
            dst_df['retrieved_at'] = datetime.utcnow().isoformat()
            
            # Save to CSV
            dst_df.to_csv(output_csv, index=False)
            logger.info(f"Saved {len(dst_df)} DST props to {output_csv}")
            
            return dst_df
        else:
            logger.warning("No DST props data fetched")
            return pd.DataFrame(columns=['team', 'market', 'line', 'over_odds', 'under_odds', 'book', 'game_id', 'season', 'week'])

    except Exception as e:
        logger.error(f"Error in fetch_dst_props_for_week: {e}")
        raise


@retry(stop=stop_after_attempt(3), wait=wait_exponential_jitter(initial=1, max=8))
def _fetch_dst_props_with_retry(game_id: str, home_team: str, away_team: str) -> List[Dict]:
    """Fetch DST props for a single game with retry logic."""
    try:
        from src.app.io.odds import dst_props
        from src.app.utils.normalize import normalize_team
    except ImportError:
        return []

    try:
        logger.info(f"Fetching DST props for {away_team} @ {home_team}")
        
        # Call the Odds API for DST props
        dst_data = dst_props(game_id, regions="us,us2", oddsFormat="american")
        
        if not dst_data:
            logger.warning(f"No DST props data returned for game {game_id}")
            return []

        # Parse the response to extract DST props
        game_dst_props = []
        
        # The API response structure for DST props
        if isinstance(dst_data, dict) and 'bookmakers' in dst_data:
            for bookmaker in dst_data['bookmakers']:
                if 'markets' in bookmaker:
                    for market in bookmaker['markets']:
                        market_key = market.get('key', '')
                        
                        # Map API market keys to our internal format
                        market_mapping = {
                            'team_sacks': 'sacks',
                            'team_turnovers': 'turnovers', 
                            'team_defensive_tds': 'def_td',
                            'team_safeties': 'safety',
                            'team_blocked_kicks': 'blocked_kick'
                        }
                        
                        if market_key in market_mapping:
                            internal_market = market_mapping[market_key]
                            
                            # Extract outcomes for both teams
                            for outcome in market.get('outcomes', []):
                                team_name = outcome.get('name', '')
                                line = outcome.get('point', 0)
                                over_odds = outcome.get('price', 0)
                                
                                # Determine which team this outcome is for
                                if team_name in [home_team, away_team]:
                                    normalized_team = normalize_team(team_name) if 'normalize_team' in locals() else team_name
                                    opponent = away_team if team_name == home_team else home_team
                                    normalized_opponent = normalize_team(opponent) if 'normalize_team' in locals() else opponent
                                    
                                    game_dst_props.append({
                                        'team': normalized_team,
                                        'opponent': normalized_opponent,
                                        'market': internal_market,
                                        'line': line,
                                        'over_odds': over_odds,
                                        'under_odds': None,  # Usually not provided for DST props
                                        'game_id': game_id,
                                        'book': bookmaker.get('title', 'unknown')
                                    })
        
        return game_dst_props

    except Exception as e:
        logger.error(f"Error fetching DST props for {away_team} @ {home_team}: {e}")
        return []


def calculate_dst_projections(df: pd.DataFrame, season: int, week: int) -> pd.DataFrame:
    """Calculate DST projections using props and team totals.
    
    DraftKings DST Scoring:
    - Sacks = line × 1 pt
    - Turnovers (INT/FUM) = line × 2 pts  
    - Def/ST TD = prob × 6 pts
    - Safety = prob × 2 pts
    - Blocked Kick = prob × 2 pts
    - Points Allowed = from opponent implied total:
      0 = +10, 1–6 = +7, 7–13 = +4, 14–20 = +1, 21–27 = 0, 28–34 = -1, 35+ = -4
    """
    if df.empty:
        return df
    
    # Filter for DST positions
    dst_mask = df.get('Position', df.get('Pos', '')).str.upper().isin(['DST', 'DEF', 'D'])
    if not dst_mask.any():
        return df
    
    dst_df = df[dst_mask].copy()
    
    # Load team totals and DST props
    try:
        team_totals_df = fetch_team_totals_for_week(season, week, force=False)
        dst_props_df = fetch_dst_props_for_week(season, week, force=False)
    except Exception as e:
        logger.warning(f"Could not load team totals or DST props: {e}")
        # Add empty DST columns
        for col in ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']:
            if col not in dst_df.columns:
                dst_df[col] = 0.0
        return df
    
    # Merge team totals for points allowed calculation
    team_col = 'Team' if 'Team' in dst_df.columns else None
    if team_col and not team_totals_df.empty:
        # Normalize team names
        dst_df['team_norm'] = dst_df[team_col].astype(str).str.upper()
        team_totals_df['team_norm'] = team_totals_df['team'].astype(str).str.upper()
        
        # Merge to get opponent implied totals
        dst_df = dst_df.merge(
            team_totals_df[['team_norm', 'implied_total']].drop_duplicates(),
            on='team_norm',
            how='left'
        )
        
        # Calculate points allowed scoring
        dst_df['DST_PtsAllowed'] = dst_df['implied_total'].apply(_calculate_points_allowed_score)
    else:
        dst_df['DST_PtsAllowed'] = 0.0
    
    # Merge DST props for other scoring components
    if not dst_props_df.empty and team_col:
        # Pivot DST props to get one row per team
        dst_props_pivot = dst_props_df.pivot_table(
            index='team',
            columns='market',
            values='line',
            aggfunc='median'
        ).reset_index()
        
        # Normalize team names
        dst_props_pivot['team_norm'] = dst_props_pivot['team'].astype(str).str.upper()
        
        # Merge DST props
        dst_df = dst_df.merge(
            dst_props_pivot,
            on='team_norm',
            how='left'
        )
        
        # Calculate DST scoring components
        dst_df['DST_Sacks'] = dst_df.get('sacks', 0) * 1.0  # 1 pt per sack
        dst_df['DST_TO'] = dst_df.get('turnovers', 0) * 2.0  # 2 pts per turnover
        dst_df['DST_TD'] = dst_df.get('def_td', 0) * 6.0  # 6 pts per def TD
        dst_df['DST_Safety'] = dst_df.get('safety', 0) * 2.0  # 2 pts per safety
        dst_df['DST_Block'] = dst_df.get('blocked_kick', 0) * 2.0  # 2 pts per blocked kick
    else:
        # Set to 0 if no props data
        for col in ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block']:
            dst_df[col] = 0.0
    
    # Calculate total DST projection
    dst_df['DST_ProjPoints'] = (
        dst_df['DST_Sacks'] + 
        dst_df['DST_TO'] + 
        dst_df['DST_TD'] + 
        dst_df['DST_Safety'] + 
        dst_df['DST_Block'] + 
        dst_df['DST_PtsAllowed']
    )
    
    # Update the main DataFrame
    df.loc[dst_mask, ['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']] = dst_df[['DST_Sacks', 'DST_TO', 'DST_TD', 'DST_Safety', 'DST_Block', 'DST_PtsAllowed', 'DST_ProjPoints']].values
    
    return df


def _calculate_points_allowed_score(implied_total: float) -> float:
    """Calculate DraftKings points allowed scoring based on opponent implied total."""
    if pd.isna(implied_total):
        return 0.0
    
    # DraftKings points allowed scoring
    if implied_total == 0:
        return 10.0
    elif 1 <= implied_total <= 6:
        return 7.0
    elif 7 <= implied_total <= 13:
        return 4.0
    elif 14 <= implied_total <= 20:
        return 1.0
    elif 21 <= implied_total <= 27:
        return 0.0
    elif 28 <= implied_total <= 34:
        return -1.0
    else:  # 35+
        return -4.0
