from __future__ import annotations

import os
import pandas as pd
import logging

logger = logging.getLogger(__name__)

from .context import SEASON_DEFAULT
from .vegas import attach_vegas
from .stats_loader import merge_usage_into_dk
from .defense import attach_defense
from .sim import simulate_players
from .game_context import attach_game_context
from .props import fetch_props_for_week, merge_props_expected, _load_alias_map, _normalize_player_name, save_unmatched_players

from .usage import get_qb_share, get_rb_share, get_wr_te_share

# Optional props-based baseline support via Odds API
try:
    from app.io.odds import list_events, event_odds  # type: ignore
    from app.etl.props_parser import build_props_table  # type: ignore
    from app.models.dk_points import proj_from_props  # type: ignore
    from app.utils.normalize import normalize_team  # type: ignore
except Exception:  # pragma: no cover
    list_events = None  # type: ignore
    event_odds = None  # type: ignore
    build_props_table = None  # type: ignore
    proj_from_props = None  # type: ignore
    normalize_team = None  # type: ignore


def _props_proj_for_slate(dk_df: pd.DataFrame, week: int | None) -> pd.DataFrame:
    """Build props-derived DK projections for players on the slate.

    Returns DataFrame with columns: name_norm, dk_proj
    """
    if week is None:
        return pd.DataFrame(columns=["name_norm", "dk_proj"])  # no week context
    if list_events is None or event_odds is None or build_props_table is None or proj_from_props is None:
        return pd.DataFrame(columns=["name_norm", "dk_proj"])  # odds client unavailable

    # Collect slate teams (normalized)
    teams = []
    if "Team" in dk_df.columns:
        teams = (
            dk_df["Team"].dropna().astype(str)
            .map(lambda x: normalize_team(x) if normalize_team else str(x).upper())
            .dropna().unique().tolist()
        )
    if not teams:
        return pd.DataFrame(columns=["name_norm", "dk_proj"])  # no team keys

    rows = []
    try:
        events = list_events()
    except Exception:
        events = []
    for ev in events or []:
        try:
            home = normalize_team(ev.get("home_team", "")) if normalize_team else str(ev.get("home_team", "")).upper()
            away = normalize_team(ev.get("away_team", "")) if normalize_team else str(ev.get("away_team", "")).upper()
        except Exception:
            home = str(ev.get("home_team", "")).upper(); away = str(ev.get("away_team", "")).upper()
        if home in teams or away in teams:
            eid = ev.get("id")
            if not eid:
                continue
            try:
                odds = event_odds(str(eid))
                pf = build_props_table(odds, {"home_team": ev.get("home_team", ""), "away_team": ev.get("away_team", "")})
                proj = proj_from_props(pf)
                rows.append(proj)
            except Exception:
                continue
    if not rows:
        return pd.DataFrame(columns=["name_norm", "dk_proj"])  # nothing matched
    props = pd.concat(rows, ignore_index=True)
    props["name_norm"] = props["player"].astype(str).str.lower()
    out = props.groupby("name_norm")["dk_proj"].median().reset_index()
    return out


def _convert_props_to_dk_fp(expected_stats: pd.DataFrame) -> pd.Series:
    """Convert expected stats from props to DK fantasy points.
    
    Uses official DK scoring:
    QB: 0.04 * pass_yds + 4 * pass_tds -1 * ints
    RB/WR/TE: 0.1 * rec_yds + 1 * rec + 6 * rec_tds; 0.1 * rush_yds + 6 * rush_tds
    """
    if expected_stats.empty:
        return pd.Series([], dtype=float)
    
    # Initialize DK points
    dk_points = pd.Series(0.0, index=expected_stats.index, dtype=float)
    
    # Add passing stats (for QBs)
    if 'Exp_PassYds' in expected_stats.columns:
        dk_points += 0.04 * expected_stats['Exp_PassYds'].fillna(0.0)
    if 'Exp_PassTD' in expected_stats.columns:
        dk_points += 4.0 * expected_stats['Exp_PassTD'].fillna(0.0)
    if 'Exp_Int' in expected_stats.columns:
        dk_points -= 1.0 * expected_stats['Exp_Int'].fillna(0.0)
    
    # Add receiving stats (for RB/WR/TE)
    if 'Exp_RecYds' in expected_stats.columns:
        dk_points += 0.1 * expected_stats['Exp_RecYds'].fillna(0.0)
    if 'Exp_Receptions' in expected_stats.columns:
        dk_points += 1.0 * expected_stats['Exp_Receptions'].fillna(0.0)
    if 'Exp_RecTD' in expected_stats.columns:
        dk_points += 6.0 * expected_stats['Exp_RecTD'].fillna(0.0)
    
    # Add rushing stats (for RB/WR/TE)
    if 'Exp_RushYds' in expected_stats.columns:
        dk_points += 0.1 * expected_stats['Exp_RushYds'].fillna(0.0)
    if 'Exp_RushTD' in expected_stats.columns:
        dk_points += 6.0 * expected_stats['Exp_RushTD'].fillna(0.0)
    
    return dk_points


def merge_props_expected_new(df: pd.DataFrame, season: int, week: int, strict: bool = False) -> pd.DataFrame:
    """Merge props-derived expected stats and perform Bayesian blending.

    Requires props CSV presence when called. Implements position-based Bayesian blending.
    Under strict mode, validates props coverage and fails if insufficient.
    """
    # Require props CSV - no fallback
    props_csv = f"data/props/props_{season}_week{week}.csv"
    if not os.path.exists(props_csv):
        raise FileNotFoundError(f"Props CSV required but not found: {props_csv}. Run 'python -m src.cli props --season {season} --week {week} --force' first.")

    try:
        # Load props data
        props_df = pd.read_csv(props_csv)
        if props_df.empty:
            raise ValueError(f"Props CSV is empty: {props_csv}. Re-run props fetch.")

        # Use the new merge function from props.py
        from .props import merge_props_expected
        merged = merge_props_expected(df, props_df)

        # Strict validation: check props coverage for offensive players
        if strict:
            _validate_props_coverage(merged)

        # Apply Bayesian blending
        merged = _apply_bayesian_blending_with_props(merged)

        return merged

    except Exception as e:
        logger.error(f"Error merging props expected stats: {e}")
        raise  # No fallback - always re-raise


def _validate_props_coverage(df: pd.DataFrame) -> None:
    """Validate props coverage under strict mode."""
    # Get offensive players (QB/RB/WR/TE)
    pos_col = None
    for cand in ("Position", "Roster Position", "Pos", "pos"):
        if cand in df.columns:
            pos_col = cand
            break

    if pos_col is None:
        logger.warning("No position column found for props coverage validation")
        return

    pos_series = df[pos_col].astype(str).str.upper()
    offensive_mask = (
        pos_series.str.contains("QB", na=False) |
        pos_series.str.contains("RB", na=False) |
        pos_series.str.contains("WR", na=False) |
        pos_series.str.contains("TE", na=False)
    )

    offensive_players = df[offensive_mask]
    if len(offensive_players) == 0:
        return

    # Check props coverage
    covered_players = offensive_players[offensive_players['PropsCoverage'] == 1]
    coverage_pct = len(covered_players) / len(offensive_players)

    if coverage_pct < 0.5:
        raise ValueError(
            f"Props coverage too low: {coverage_pct:.1%} ({len(covered_players)}/{len(offensive_players)}) "
            f"of offensive players have props data. Minimum required: 50%"
        )


def _apply_bayesian_blending_with_props(df: pd.DataFrame) -> pd.DataFrame:
    """Apply Bayesian blending between FC baseline and props-derived projections.

    Uses position-based weights and clamps to realistic ranges.
    Blends FC baseline (primary) + props FP (secondary) + AnytimeTD_FP (weight w_td=0.3)
    """
    result = df.copy()

    # Get position column
    pos_col = None
    for cand in ("Position", "Roster Position", "Pos", "pos"):
        if cand in result.columns:
            pos_col = cand
            break

    if pos_col is None:
        logger.warning("No position column found for Bayesian blending")
        return result

    # Get FC baseline (should be in ProjPoints already)
    if 'ProjPoints' not in result.columns:
        logger.warning("No ProjPoints column found for blending")
        return result

    fc_base = result['ProjPoints'].fillna(0.0)
    props_fp = result.get('expected_fp', pd.Series(0.0, index=result.index))
    anytime_td_fp = result.get('AnytimeTD_FP', pd.Series(0.0, index=result.index))

    # Position-based blending weights
    pos_series = result[pos_col].astype(str).str.upper()
    blended_proj = pd.Series(0.0, index=result.index)

    # Define position-specific weights
    position_weights = {
        'QB': {'w_fc': 0.7, 'w_props': 0.25, 'w_td': 0.05},
        'RB': {'w_fc': 0.6, 'w_props': 0.25, 'w_td': 0.15},
        'WR': {'w_fc': 0.6, 'w_props': 0.25, 'w_td': 0.15},
        'TE': {'w_fc': 0.6, 'w_props': 0.25, 'w_td': 0.15}
    }

    # Apply blending by position
    for pos, weights in position_weights.items():
        pos_mask = pos_series.str.contains(pos, na=False)
        if pos_mask.any():
            w_fc = weights['w_fc']
            w_props = weights['w_props']
            w_td = weights['w_td']

            # Blend: weighted average of FC baseline, props, and Anytime TD FP
            pos_blended = (
                w_fc * fc_base.loc[pos_mask] +
                w_props * props_fp.loc[pos_mask] +
                w_td * anytime_td_fp.loc[pos_mask]
            )

            # Clamp to realistic range (80-120% of FC baseline)
            lower_bound = 0.8 * fc_base.loc[pos_mask]
            upper_bound = 1.2 * fc_base.loc[pos_mask]
            pos_blended = pos_blended.clip(lower=lower_bound, upper=upper_bound)

            blended_proj.loc[pos_mask] = pos_blended

    # For positions not covered, use FC baseline
    covered_mask = blended_proj > 0
    blended_proj.loc[~covered_mask] = fc_base.loc[~covered_mask]

    # Update ProjPoints with blended values
    result['ProjPoints'] = blended_proj

    return result


def _apply_bayesian_blending(df: pd.DataFrame, props_fp_map: pd.Series) -> pd.DataFrame:
    """Apply Bayesian blending between FC baseline and props-derived projections.
    
    Uses position-based weights and clamps to realistic ranges.
    """
    result = df.copy()
    
    # Get position column
    pos_col = None
    for cand in ("Position", "Roster Position", "Pos", "pos"):
        if cand in result.columns:
            pos_col = cand
            break
    
    if pos_col is None:
        return result
    
    # Initialize blended projection
    fc_base = result.get('ProjPoints', pd.Series(0.0, index=result.index))
    
    # Get props FP for each player
    props_fp = result['name_norm'].map(props_fp_map).fillna(0.0)
    
    # Position-based blending weights
    pos_weights = {
        'QB': {'w_fc': 0.7, 'w_props': 0.3},
        'RB': {'w_fc': 0.6, 'w_props': 0.4},
        'WR': {'w_fc': 0.6, 'w_props': 0.4},
        'TE': {'w_fc': 0.6, 'w_props': 0.4}
    }
    
    # Apply blending by position
    blended_proj = pd.Series(0.0, index=result.index, dtype=float)
    
    for pos, weights in pos_weights.items():
        pos_mask = result[pos_col].str.contains(pos, case=False, na=False)
        if pos_mask.any():
            w_fc = weights['w_fc']
            w_props = weights['w_props']
            
            # Blend: weighted average of FC baseline and props
            pos_blended = (
                w_fc * fc_base.loc[pos_mask] + 
                w_props * props_fp.loc[pos_mask]
            )
            
            # Clamp to realistic range (80-120% of FC baseline)
            lower_bound = 0.8 * fc_base.loc[pos_mask]
            upper_bound = 1.2 * fc_base.loc[pos_mask]
            pos_blended = pos_blended.clip(lower=lower_bound, upper=upper_bound)
            
            blended_proj.loc[pos_mask] = pos_blended
    
    # For positions not covered, use FC baseline
    covered_mask = blended_proj > 0
    blended_proj.loc[~covered_mask] = fc_base.loc[~covered_mask]
    
    # Update ProjPoints with blended values
    result['ProjPoints'] = blended_proj
    
    # Add props metadata
    props_attached = props_fp > 0
    result['props_attached'] = props_attached
    result['props_fp'] = props_fp
    
    return result


def _apply_bayesian_blending_with_td(df: pd.DataFrame, props_fp_map: pd.Series) -> pd.DataFrame:
    """Apply Bayesian blending between FC baseline and props-derived projections with Anytime TD integration.
    
    Uses position-based weights and clamps to realistic ranges.
    Includes Anytime TD expected fantasy points with modest weight.
    """
    result = df.copy()
    
    # Get position column
    pos_col = None
    for cand in ("Position", "Roster Position", "Pos", "pos"):
        if cand in result.columns:
            pos_col = cand
            break
    
    if pos_col is None:
        return result
    
    # Initialize blended projection
    fc_base = result.get('ProjPoints', pd.Series(0.0, index=result.index))
    
    # Get props FP for each player
    props_fp = result['name_norm'].map(props_fp_map).fillna(0.0)
    
    # Get Anytime TD expected fantasy points if available
    anytime_td_fp = result.get('AnytimeTD_FP', pd.Series(0.0, index=result.index))
    
    # Position-based blending weights
    pos_weights = {
        'QB': {'w_fc': 0.7, 'w_props': 0.3, 'w_td': 0.3},
        'RB': {'w_fc': 0.6, 'w_props': 0.4, 'w_td': 0.3},
        'WR': {'w_fc': 0.6, 'w_props': 0.4, 'w_td': 0.3},
        'TE': {'w_fc': 0.6, 'w_props': 0.4, 'w_td': 0.3}
    }
    
    # Apply blending by position
    blended_proj = pd.Series(0.0, index=result.index, dtype=float)
    
    for pos, weights in pos_weights.items():
        pos_mask = result[pos_col].str.contains(pos, case=False, na=False)
        if pos_mask.any():
            w_fc = weights['w_fc']
            w_props = weights['w_props']
            w_td = weights['w_td']
            
            # Blend: weighted average of FC baseline, props, and Anytime TD FP
            pos_blended = (
                w_fc * fc_base.loc[pos_mask] + 
                w_props * props_fp.loc[pos_mask] +
                w_td * anytime_td_fp.loc[pos_mask]
            )
            
            # Clamp to realistic range (80-120% of FC baseline)
            lower_bound = 0.8 * fc_base.loc[pos_mask]
            upper_bound = 1.2 * fc_base.loc[pos_mask]
            pos_blended = pos_blended.clip(lower=lower_bound, upper=upper_bound)
            
            blended_proj.loc[pos_mask] = pos_blended
    
    # For positions not covered, use FC baseline
    covered_mask = blended_proj > 0
    blended_proj.loc[~covered_mask] = fc_base.loc[~covered_mask]
    
    # Update ProjPoints with blended values
    result['ProjPoints'] = blended_proj
    
    # Add props metadata
    props_attached = props_fp > 0
    result['props_attached'] = props_attached
    result['props_fp'] = props_fp
    
    # Add Anytime TD metadata
    td_attached = anytime_td_fp > 0
    result['td_attached'] = td_attached
    
    return result
def attach_projections(
    dk_df: pd.DataFrame,
    week: int | None = None,
    *,
    season: int = SEASON_DEFAULT,
    with_usage: bool = False,
    with_vegas: bool = True,
    with_context: bool = False,
    with_props: bool = False,
    strict: bool = False,
) -> pd.DataFrame:
    """Attach projections to DK NFL DF while preserving all original columns.

    - Adds columns: ProjPoints, Floor, Ceiling, Ownership
    - Uses best-available proxy among common columns for base projection
    - Optionally adds VegasPts and scales projections by team context (with_vegas)
      proj *= VegasPts / league_avg
    - Merges usage metrics and defense (not exported unless caller includes them)
    - Optionally applies usage-based multipliers by role (with_usage)
    - Optionally attaches game context (spread, total, pace) (with_context)
    - Optionally blends props-derived expectations (with_props)
    - Computes distribution stats via Monte Carlo
    - Guarantees row count is unchanged
    """
    if dk_df is None or dk_df.empty:
        return dk_df.assign(
            ProjPoints=pd.Series([], dtype=float),
            Floor=pd.Series([], dtype=float),
            Ceiling=pd.Series([], dtype=float),
            Ownership=pd.Series([], dtype=float),
        )

    # Merge usage/context (safe no-ops if data not available)
    df = merge_usage_into_dk(dk_df, week)
    
    # Attach game context (spread, total, pace) if requested
    if with_context and week is not None:
        df = attach_game_context(df, week, season)
    
    if with_vegas:
        df = attach_vegas(df, week, season=season)
    df = attach_defense(df, week)

    # Build baseline with explicit source tagging and props fallback
    out = df.copy()

    # Prepare helper keys
    if "name_norm" not in out.columns:
        # Attempt to derive from Name/Player
        if "Name" in out.columns:
            out["name_norm"] = out["Name"].astype(str).str.replace(r"\s+"," ", regex=True).str.strip().str.lower()
        elif "Player" in out.columns:
            out["name_norm"] = out["Player"].astype(str).str.replace(r"\s+"," ", regex=True).str.strip().str.lower()
        else:
            out["name_norm"] = ""

    baseline = pd.Series(0.0, index=out.index, dtype=float)
    baseline_src = pd.Series("zero", index=out.index, dtype=object)

    # 1) FC Proj if > 0 (primary source)
    if "FC Proj" in out.columns:
        fc = pd.to_numeric(out["FC Proj"], errors="coerce").fillna(0.0)
        mask = fc > 0
        baseline.loc[mask] = fc.loc[mask]
        baseline_src.loc[mask] = "fc_proj"

    # 2) Props-derived baseline for remaining rows (only if FC Proj is 0 or missing)
    need_props = baseline_src.eq("zero").any()
    props_map = None
    if need_props:
        props_df = _props_proj_for_slate(out, week)
        if not props_df.empty:
            props_map = props_df.set_index("name_norm")["dk_proj"]
            hits = out["name_norm"].map(props_map)
            mask = baseline_src.eq("zero") & hits.notna() & (hits > 0)
            baseline.loc[mask] = hits.loc[mask].astype(float)
            baseline_src.loc[mask] = "props"

    # NO HISTORICAL AVERAGES - too dangerous when FC Proj is 0 (could be injured)
    # Players without FC Proj or props data remain at 0 (safe default)

    out["BaselineSource"] = baseline_src
    proj = baseline.astype(float)
    out["ProjPoints"] = proj  # Ensure ProjPoints exists for props blending

    # Vegas scaling (clamped to +/-20%)
    vegas = pd.to_numeric(df.get("VegasPts", pd.Series([None] * len(df), index=df.index)), errors="coerce")
    if with_vegas and "Team" in df.columns:
        team_avgs = df.groupby("Team")["VegasPts"].mean(numeric_only=True)
        league_avg = float(team_avgs.mean()) if not team_avgs.empty else float(pd.to_numeric(vegas, errors="coerce").mean(skipna=True) or 21.0)
    else:
        league_avg = float(pd.to_numeric(vegas, errors="coerce").mean(skipna=True) or 21.0)

    vegas_mult = (vegas / league_avg).fillna(1.0) if with_vegas else pd.Series(1.0, index=df.index)
    vegas_mult = vegas_mult.clip(lower=0.8, upper=1.2)



    # Optional usage-based multiplier (small effect, clamped to +/-15%)
    usage_mult = pd.Series(1.0, index=out.index, dtype=float)
    usage_meta = pd.Series("none", index=out.index, dtype=object)
    if with_usage:
        # Determine best-available columns
        pos_col = None
        for cand in ("Position", "Roster Position", "Pos", "pos"):
            if cand in out.columns:
                pos_col = cand
                break
        team_col = "Team" if "Team" in out.columns else None

        # Try real usage CSV overrides first
        from .usage import load_week_usage_csv  # local import to avoid cycle in tests
        u = load_week_usage_csv(week, season=season) if week is not None else pd.DataFrame()
        if team_col and pos_col and not u.empty:
            tmp = out.copy()
            tmp["TEAM"] = tmp[team_col].astype(str).str.upper()
            key_cols = ["name_norm", "TEAM"]
            if "ID" in tmp.columns and "ID" in u.columns:
                key_cols = ["ID"]
            tmp = tmp.merge(u, on=key_cols, how="left")
            # Where share present, compute relative to team-position average
            pos_txt = tmp[pos_col].astype(str)
            def _pos_grp(s: str) -> str:
                s = s.upper()
                if "QB" in s: return "QB"
                if "RB" in s: return "RB"
                if "TE" in s: return "TE"
                if "WR" in s: return "WR"
                return "OTH"
            tmp["_POS_GRP"] = pos_txt.map(_pos_grp)
            grp_keys = ["TEAM", "_POS_GRP"]
            # Average share for group where available
            grp_avg = tmp.groupby(grp_keys)["share"].transform(lambda x: x.mean(skipna=True))
            has_share = tmp["share"].notna() & (tmp["share"] > 0)
            # Compute adjustment for those with shares
            adj = (tmp["share"] / grp_avg).where(has_share, 1.0)
            usage_mult.loc[has_share] = adj.loc[has_share].astype(float)
            usage_meta.loc[has_share] = "from_csv"
            out["UsageAdj"] = usage_mult.astype(float)
            # For rows without share, fall back to static defaults below
            out.loc[~has_share, "UsageAdj"] = 1.0

        # Static defaults for any remaining rows not covered by CSV
        if team_col and pos_col:
            # Precompute ranks by team-position using Salary (fallback: AvgPtsPerGame)
            if "Salary" in out.columns:
                rank_key = pd.to_numeric(out["Salary"], errors="coerce").fillna(0.0)
            else:
                rank_key = pd.to_numeric(out.get("AvgPtsPerGame", 0.0), errors="coerce").fillna(0.0)

            pos_txt2 = out[pos_col].astype(str)
            remaining = usage_meta.ne("from_csv")

            # QB group
            is_qb = remaining & pos_txt2.str.contains("QB", case=False, na=False)
            for team, idxs in out[is_qb].groupby(team_col).groups.items():
                idxs = list(idxs)
                ranked = out.loc[idxs].assign(_rk=(-rank_key.loc[idxs])).sort_values("_rk").index.tolist()
                shares = get_qb_share(team)
                seq = [shares.get(f"QB{i+1}", 0.0) for i in range(len(ranked))]
                avg = (sum(seq) / len(seq)) if seq else 1.0
                avg = avg if avg > 0 else 1.0
                for i, ridx in enumerate(ranked):
                    s = seq[i] if i < len(seq) else 0.0
                    usage_mult.loc[ridx] = s / avg
                    usage_meta.loc[ridx] = "static_defaults"

            # RB group
            is_rb = remaining & pos_txt2.str.contains("RB", case=False, na=False)
            for team, idxs in out[is_rb].groupby(team_col).groups.items():
                idxs = list(idxs)
                ranked = out.loc[idxs].assign(_rk=(-rank_key.loc[idxs])).sort_values("_rk").index.tolist()
                shares = get_rb_share(team)
                seq = [shares.get(f"RB{i+1}", 0.0) for i in range(len(ranked))]
                avg = (sum(seq) / len(seq)) if seq else 1.0
                avg = avg if avg > 0 else 1.0
                for i, ridx in enumerate(ranked):
                    s = seq[i] if i < len(seq) else 0.0
                    usage_mult.loc[ridx] = s / avg
                    usage_meta.loc[ridx] = "static_defaults"

            # WR/TE combined pass-catchers
            is_wr = remaining & pos_txt2.str.contains("WR", case=False, na=False)
            is_te = remaining & pos_txt2.str.contains("TE", case=False, na=False)
            is_pc = is_wr | is_te
            for team, idxs in out[is_pc].groupby(team_col).groups.items():
                idxs = list(idxs)
                wr_mask = is_wr.loc[idxs]
                te_mask = is_te.loc[idxs]
                ranked_wr = out.loc[idxs][wr_mask].assign(_rk=(-rank_key.loc[idxs][wr_mask])).sort_values("_rk").index.tolist()
                ranked_te = out.loc[idxs][te_mask].assign(_rk=(-rank_key.loc[idxs][te_mask])).sort_values("_rk").index.tolist()
                shares = get_wr_te_share(team)
                wr_seq = [shares.get(f"WR{i+1}", 0.0) for i in range(len(ranked_wr))]
                te_seq = [shares.get(f"TE{i+1}", 0.0) for i in range(len(ranked_te))]
                combined = wr_seq + te_seq
                avg = (sum(combined) / len(combined)) if combined else 1.0
                avg = avg if avg > 0 else 1.0
                for i, ridx in enumerate(ranked_wr):
                    s = wr_seq[i] if i < len(wr_seq) else 0.0
                    usage_mult.loc[ridx] = s / avg
                    usage_meta.loc[ridx] = "static_defaults"
                for i, ridx in enumerate(ranked_te):
                    s = te_seq[i] if i < len(te_seq) else 0.0
                    usage_mult.loc[ridx] = s / avg
                    usage_meta.loc[ridx] = "static_defaults"

        # Clamp usage effect to small range
        usage_mult = usage_mult.clip(lower=0.9, upper=1.1)
        out["UsageAdj"] = usage_mult.astype(float)
    else:
        usage_meta[:] = "none"

    # Defense matchup multiplier (small, +/-10%)
    pos_col = None
    for cand in ("Position", "Roster Position", "Pos", "pos"):
        if cand in out.columns:
            pos_col = cand
            break
    pos_txt = out[pos_col].astype(str) if pos_col else pd.Series(["" for _ in range(len(out))], index=out.index)

    d_qb = pd.to_numeric(out.get("dvoa_vs_qb", pd.Series([1.0]*len(out), index=out.index)), errors="coerce").fillna(1.0)
    d_rb = pd.to_numeric(out.get("dvoa_vs_rb", pd.Series([1.0]*len(out), index=out.index)), errors="coerce").fillna(1.0)
    d_wr = pd.to_numeric(out.get("dvoa_vs_wr", pd.Series([1.0]*len(out), index=out.index)), errors="coerce").fillna(1.0)
    d_te = pd.to_numeric(out.get("dvoa_vs_te", pd.Series([1.0]*len(out), index=out.index)), errors="coerce").fillna(1.0)

    def_mult = pd.Series(1.0, index=out.index, dtype=float)
    m_qb = pos_txt.str.contains("QB", case=False, na=False)
    m_rb = pos_txt.str.contains("RB", case=False, na=False)
    m_wr = pos_txt.str.contains("WR", case=False, na=False)
    m_te = pos_txt.str.contains("TE", case=False, na=False)
    def_mult.loc[m_qb] = d_qb.loc[m_qb]
    def_mult.loc[m_rb] = d_rb.loc[m_rb]
    def_mult.loc[m_wr] = d_wr.loc[m_wr]
    def_mult.loc[m_te] = d_te.loc[m_te]
    def_mult = def_mult.clip(lower=0.9, upper=1.1)

    # Apply props blending if requested (BEFORE other adjustments to blend with FC baseline)
    if with_props and week is not None:
        out = merge_props_expected_new(out, season, week, strict=strict)
        # Update the baseline after props blending
        proj = out["ProjPoints"].copy()

    # Combine multipliers and cap net effect to +/-20%
    total_mult = (vegas_mult * (usage_mult if with_usage else 1.0) * def_mult).astype(float)
    total_mult = total_mult.clip(lower=0.8, upper=1.2)

    out["ProjPoints"] = (proj * total_mult).astype(float)

    # Simple distribution bounds around mean
    out["Floor"] = (0.7 * out["ProjPoints"]).astype(float)
    out["Ceiling"] = (1.3 * out["ProjPoints"]).astype(float)

    # Ownership (stub): set to 0.0 for now
    out["Ownership"] = 0.0

    # Persist usage meta if computed
    if 'usage_meta' not in out.columns:
        out['usage_meta'] = usage_meta
    else:
        # Overwrite only where empty
        m = out['usage_meta'].isna() | (out['usage_meta'] == '')
        out.loc[m, 'usage_meta'] = usage_meta.loc[m]

    return out
