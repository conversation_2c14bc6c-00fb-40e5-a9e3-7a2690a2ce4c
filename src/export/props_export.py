from __future__ import annotations

import pandas as pd


def to_props_board(df_cal: pd.DataFrame, odds_join: pd.DataFrame | None = None) -> pd.DataFrame:
    """Build a simple props board from calibrated projections.

    Output columns: ["player","team","opp","market","line"]
    This is a stub that can be extended per-market. For now, we emit points market lines
    using calibrated mean and p90 as reference.
    """
    df = df_cal.copy()
    pts = pd.DataFrame(
        {
            "player": df.get("Player", df.get("player_name", df.get("player_id"))).astype(str),
            "team": df.get("team", "").astype(str),
            "opp": df.get("opp", "").astype(str),
            "market": "fantasy_points",
            "line": df.get("Projection", df.get("mean", 0.0)).astype(float),
        }
    )

    if odds_join is not None and not odds_join.empty:
        # Merge odds by player/market if available (not enforced here)
        pass

    return pts

