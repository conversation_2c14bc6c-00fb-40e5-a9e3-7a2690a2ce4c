from __future__ import annotations

from typing import Optional

import numpy as np
import pandas as pd


def to_fantasy_cruncher(df_cal: pd.DataFrame, salaries_df: pd.DataFrame, injuries_df: Optional[pd.DataFrame] = None) -> pd.DataFrame:
    """Build Fantasy Cruncher export DataFrame.

    Output columns (exact): ["Player","ID","Position","Team","Opponent","Salary","Projection","Ceiling","Floor","Ownership","InjuryStatus"]
    - Projection: calibrated mean (column 'Projection' or 'mean')
    - Ceiling: p90 (column 'Ceiling' or 'p90')
    - Floor: p15 approx via mean-1.036*std (or 'Floor' if present)
    - Ownership: blank by default
    - InjuryStatus: from injuries if provided (column 'status')
    """
    # Ensure IDs in salaries
    sals = salaries_df.copy()
    if "ID" not in sals.columns and "player_id" in sals.columns:
        sals = sals.rename(columns={"player_id": "ID"})

    # Merge on player ID; if different key is required, adjust as needed
    # We'll preserve player name if present in salaries as 'Player'
    df = df_cal.copy()
    if "player_id" in df.columns and "ID" not in df.columns:
        df = df.rename(columns={"player_id": "ID"})

    out = pd.merge(sals, df, on="ID", how="inner", suffixes=("_sal", ""))

    # Determine columns
    proj = out.get("Projection", out.get("mean", pd.Series([0.0] * len(out))))
    ceil = out.get("Ceiling", out.get("p90", proj))
    floor = out.get("Floor", out.get("mean", proj) - 1.036 * out.get("std", 5.0))

    # Injury status join
    inj_status = None
    if injuries_df is not None and not injuries_df.empty:
        inj = injuries_df.copy()
        if "player_id" in inj.columns and "ID" not in inj.columns:
            inj = inj.rename(columns={"player_id": "ID"})
        inj = inj[[c for c in inj.columns if c in ("ID", "status")]].drop_duplicates("ID")
        out = out.merge(inj, on="ID", how="left")
        inj_status = out.get("status")

    # Construct FC DataFrame
    fc = pd.DataFrame(
        {
            "Player": out.get("Player", out.get("name", out.get("player_name", out.get("ID")))).astype(str),
            "ID": out["ID"],
            "Position": out.get("pos", out.get("Position", "")).astype(str),
            "Team": out.get("team", out.get("Team", "")).astype(str),
            "Opponent": out.get("opp", out.get("Opponent", "")).astype(str),
            "Salary": out.get("Salary", out.get("salary", 0)).astype(int),
            "Projection": proj.astype(float),
            "Ceiling": ceil.astype(float),
            "Floor": floor.astype(float),
            "Ownership": "",
            "InjuryStatus": (inj_status.astype(str) if inj_status is not None else ""),
        }
    )

    # Ensure column order
    fc = fc[["Player","ID","Position","Team","Opponent","Salary","Projection","Ceiling","Floor","Ownership","InjuryStatus"]]
    return fc

