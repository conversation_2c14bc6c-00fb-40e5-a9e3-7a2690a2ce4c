from __future__ import annotations

import math
from typing import Dict, List, <PERSON><PERSON>

import numpy as np
import pandas as pd


def _rng(seed: int) -> np.random.Generator:
    return np.random.default_rng(seed)


def _ensure_cols(df: pd.DataFrame) -> pd.DataFrame:
    req = ["player_id", "team", "opp", "pos", "baseline_dk"]
    for c in req:
        if c not in df.columns:
            raise KeyError(f"simulate_slate requires column '{c}' in df_base")
    if "volatility_prior" not in df.columns:
        # fallback: 0.25 * baseline clip
        df = df.copy()
        df["volatility_prior"] = np.maximum(1.0, 0.25 * df["baseline_dk"].clip(lower=0.0))
    return df


def _group_key_game(row: pd.Series) -> Tuple[str, str]:
    t = str(row["team"]).upper()
    o = str(row["opp"]).upper()
    if t <= o:
        return (t, o)
    return (o, t)


def _build_block_corr(df_block: pd.DataFrame, mode: str = "fast") -> np.ndarray:
    """Build correlation matrix for a game block (both teams).

    Rules:
    - QB positively with own WR/TE (rho~0.35 scaled by target_share if available)
    - RB mildly negative vs QB/WR on same team (rho~-0.15 scaled by rush_share)
    - Opposing bring-back mild positive (rho~0.10)
    - 'independent' mode -> identity
    """
    n = len(df_block)
    if mode == "independent" or n <= 1:
        return np.eye(n)

    teams = df_block["team"].astype(str).str.upper().tolist()
    opps = df_block["opp"].astype(str).str.upper().tolist()
    poss = df_block["pos"].astype(str).str.upper().tolist()
    tgt_share = df_block.get("target_share", pd.Series([np.nan] * n)).astype(float).fillna(0.18).to_numpy()
    rush_share = df_block.get("rush_share", pd.Series([np.nan] * n)).astype(float).fillna(0.30).to_numpy()

    R = np.eye(n)

    # Precompute indices by team and position
    idx_by_team: Dict[str, List[int]] = {}
    for i, tm in enumerate(teams):
        idx_by_team.setdefault(tm, []).append(i)

    # Within-team rules
    for tm, idxs in idx_by_team.items():
        # Identify QBs and receivers and RBs in this team
        qbs = [i for i in idxs if poss[i] == "QB"]
        wrtes = [i for i in idxs if poss[i] in ("WR", "TE")]
        rbs = [i for i in idxs if poss[i] == "RB"]

        for q in qbs:
            for w in wrtes:
                rho = 0.35 * min(1.0, max(0.1, tgt_share[w]))  # scale by target share
                R[q, w] = R[w, q] = rho
            for r in rbs:
                rho = -0.15 * min(1.0, max(0.1, rush_share[r]))
                R[q, r] = R[r, q] = rho
        # RB vs WR/TE slight negative
        for r in rbs:
            for w in wrtes:
                R[r, w] = R[w, r] = min(R[r, w], -0.08)

    # Cross-team bring-back mild positive
    for i in range(n):
        for j in range(i + 1, n):
            if teams[i] != teams[j] and (opps[i] == teams[j] or opps[j] == teams[i]):
                # only connect primary skill positions
                if poss[i] in ("QB", "WR", "TE") or poss[j] in ("QB", "WR", "TE"):
                    R[i, j] = R[j, i] = max(R[i, j], 0.10)

    # Ensure positive semi-definite via jitter if needed
    # Add small diagonal to help Cholesky
    eps = 1e-6
    R[np.diag_indices_from(R)] = 1.0 + eps
    return R


def _sample_block(means: np.ndarray, stds: np.ndarray, R: np.ndarray, n: int, rng: np.random.Generator) -> np.ndarray:
    L = np.linalg.cholesky(R)
    z = rng.standard_normal(size=(n, len(means)))
    corr = z @ L.T
    return means + stds * corr


def simulate_slate(df_base: pd.DataFrame, n: int = 10000, seed: int = 42, mode: str = "fast") -> pd.DataFrame:
    """Monte Carlo simulate slate outcomes with team/stack-aware correlations.

    Returns per-player summary with columns:
    ["player_id","pos","team","opp","mean","p75","p90","std","boom_rate","bust_rate","sim_samples_used"]
    """
    df = _ensure_cols(df_base)
    df = df.copy()

    # Prepare distribution parameters
    means = df["baseline_dk"].to_numpy(dtype=float)
    sigmas = df["volatility_prior"].to_numpy(dtype=float)
    sigmas = np.where(np.isfinite(sigmas) & (sigmas > 0), sigmas, np.maximum(1.0, 0.25 * np.clip(means, 0, None)))

    # Salary for boom/bust thresholds
    salaries = df.get("salary", pd.Series([5000.0] * len(df))).astype(float).fillna(5000.0).to_numpy()

    # Group by game block (both teams) for correlations
    df["_game"] = df.apply(_group_key_game, axis=1)

    rng = _rng(seed)

    # Accumulators for summary stats
    sums = np.zeros(len(df))
    sums_sq = np.zeros(len(df))
    p75_list: List[float] = [0.0] * len(df)
    p90_list: List[float] = [0.0] * len(df)
    boom_cnt = np.zeros(len(df), dtype=int)
    bust_cnt = np.zeros(len(df), dtype=int)

    # To reduce memory, sample in chunks of players per block and aggregate on-the-fly
    # We'll perform full n draws but never store all samples.

    for game_key, idx in df.groupby("_game").groups.items():
        idx_list = list(idx)
        block = df.iloc[idx_list]
        means_b = means[idx_list]
        sigmas_b = sigmas[idx_list]
        salaries_b = salaries[idx_list]

        R = _build_block_corr(block, mode=mode)

        # Sample in sub-batches to avoid very large matrices if n is big
        batch = 2000 if n > 2000 else n
        draws_used = 0
        quantile_accum = [[] for _ in idx_list]

        while draws_used < n:
            m = min(batch, n - draws_used)
            sims = _sample_block(means_b, sigmas_b, R, m, rng)

            # Aggregate running sums for mean/std
            sums[idx_list] += sims.sum(axis=0)
            sums_sq[idx_list] += (sims * sims).sum(axis=0)

            # Boom/Bust thresholds
            boom_thr = 3.0 * (salaries_b / 1000.0)
            bust_thr = 1.0 * (salaries_b / 1000.0)
            boom_cnt[idx_list] += (sims > boom_thr).sum(axis=0)
            bust_cnt[idx_list] += (sims < bust_thr).sum(axis=0)

            # Collect samples for quantiles approximately by sub-sampling
            # Keep small reservoir to compute p75/p90 accurately enough
            for j in range(len(idx_list)):
                # Downsample to at most 4000 per player to keep memory small
                keep = sims[:, j]
                if len(quantile_accum[j]) < 4000:
                    quantile_accum[j].extend(list(keep[: min(len(keep), 4000 - len(quantile_accum[j]))]))
            draws_used += m

        # After block finishes, compute quantiles per player
        for k, j in enumerate(idx_list):
            arr = np.asarray(quantile_accum[k], dtype=float)
            if arr.size == 0:
                p75_list[j] = float(means_b[k])
                p90_list[j] = float(means_b[k])
            else:
                p75_list[j] = float(np.quantile(arr, 0.75))
                p90_list[j] = float(np.quantile(arr, 0.90))

    # Finalize summary
    mean = sums / float(n)
    var = sums_sq / float(n) - mean * mean
    std = np.sqrt(np.maximum(0.0, var))

    res = pd.DataFrame(
        {
            "player_id": df["player_id"].values,
            "pos": df["pos"].values,
            "team": df["team"].values,
            "opp": df["opp"].values,
            "mean": mean,
            "p75": p75_list,
            "p90": p90_list,
            "std": std,
            "boom_rate": boom_cnt / float(n),
            "bust_rate": bust_cnt / float(n),
            "sim_samples_used": np.full(len(df), n, dtype=int),
        }
    )
    return res


def summarize_sim(sim_df: pd.DataFrame) -> pd.DataFrame:
    """Return per-player summary in the canonical column set.

    If sim_df already contains the canonical columns, pass through.
    Otherwise, attempt to compute them.
    """
    cols = ["player_id", "pos", "team", "opp", "mean", "p75", "p90", "std", "boom_rate", "bust_rate", "sim_samples_used"]
    if all(c in sim_df.columns for c in cols):
        return sim_df[cols].copy()
    raise ValueError("summarize_sim expects a DataFrame from simulate_slate or with equivalent columns")

