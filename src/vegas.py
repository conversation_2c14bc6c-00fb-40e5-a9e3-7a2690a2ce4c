from __future__ import annotations

import os
from typing import Optional

import pandas as pd

# Note: We deliberately avoid network calls here. Odds API integration is out of scope.


def _load_vegas_totals_csv(week: Optional[int], season: int = 2025) -> Optional[pd.DataFrame]:
    """Load team totals from data/vegas/{season}/week{W}_totals.csv if present.

    Expected columns: team, opp (optional), team_total
    Returns a DataFrame with columns: TEAM (uppercase), team_total
    """
    if week is None:
        return None
    
    # Try season-specific directory first
    season_path = os.path.join("data", "vegas", str(season), f"week{week}_totals.csv")
    if os.path.exists(season_path):
        path = season_path
    else:
        # Fallback to old location for backwards compatibility
        path = os.path.join("data", "vegas", f"week{week}_totals.csv")
    
    if not os.path.exists(path):
        return None
    try:
        df = pd.read_csv(path)
    except Exception:
        return None
    if df.empty:
        return None
    cols = {c.lower(): c for c in df.columns}
    team_col = cols.get("team")
    total_col = cols.get("team_total")
    if not team_col or not total_col:
        return None
    out = pd.DataFrame({
        "TEAM": df[team_col].astype(str).str.upper().str.strip(),
        "team_total": pd.to_numeric(df[total_col], errors="coerce"),
    })
    out = out.dropna(subset=["team_total"]).drop_duplicates("TEAM")
    return out if not out.empty else None


def attach_vegas(dk_df: pd.DataFrame, week: Optional[int] = None, season: int = 2025) -> pd.DataFrame:
    """Ensure a 'VegasPts' column and a 'vegas_meta' column on the DK dataframe.

    Behavior:
    - If 'VegasPts' already exists, leave values as-is and set vegas_meta='existing'.
    - Else if a CSV exists at data/vegas/{season}/week{W}_totals.csv, join and set vegas_meta='from_csv'
      for matched rows; unmatched rows fall back to neutral and vegas_meta='neutral'.
    - Else fill neutral default (21.0) and vegas_meta='neutral'.
    """
    out = dk_df.copy()

    if "VegasPts" in out.columns:
        if "vegas_meta" not in out.columns:
            out["vegas_meta"] = "existing"
        return out

    # Try to load from CSV
    vdf = _load_vegas_totals_csv(week, season=season)
    if vdf is not None:
        left = out.copy()
        # Normalize team key
        if "Team" in left.columns:
            left["Team"] = left["Team"].astype(str).str.upper()
        # Merge
        m = left.merge(vdf, left_on="Team", right_on="TEAM", how="left", indicator=True)
        out = m.drop(columns=[c for c in ("TEAM",) if c in m.columns])
        # Assign VegasPts
        out["VegasPts"] = pd.to_numeric(out["team_total"], errors="coerce")
        # Defaults for missing
        neutral_mask = out["VegasPts"].isna()
        out.loc[neutral_mask, "VegasPts"] = 21.0
        out["vegas_meta"] = "from_csv"
        out.loc[neutral_mask, "vegas_meta"] = "neutral"
        out = out.drop(columns=[c for c in ("team_total", "_merge") if c in out.columns])
        return out

    # Fallback neutral default
    out["VegasPts"] = 21.0
    out["vegas_meta"] = "neutral"
    return out
