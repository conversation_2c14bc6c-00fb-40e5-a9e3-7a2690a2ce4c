from __future__ import annotations

import os
from typing import Optional

import pandas as pd


DEF_DIR = os.path.join("data", "defense")


def _load_week_csv(week: int) -> pd.DataFrame:
    os.makedirs(DEF_DIR, exist_ok=True)
    # Flexible: allow either a single file per week or multiple components later
    for name in [f"week{week}.csv", f"defense_week{week}.csv"]:
        path = os.path.join(DEF_DIR, name)
        if os.path.exists(path):
            try:
                df = pd.read_csv(path)
                return df
            except Exception:
                pass
    # Empty with expected columns
    return pd.DataFrame(columns=[
        "Team",  # defensive team key (opponent of player)
        "dvoa_vs_qb","dvoa_vs_rb","dvoa_vs_wr","dvoa_vs_te",
        "explosive_allowed","sack_rate","rz_td_allowed"
    ])


def attach_defense(df: pd.DataFrame, week: Optional[int]) -> pd.DataFrame:
    """Attach defensive matchup factors by opponent team and add defense_meta column.

    Expected input columns: 'Team' (player team), 'Opponent' or 'Opp' (defense). If none, returns unchanged.
    Defense CSV is keyed by defensive team code in column 'Team'.
    """
    if week is None:
        return df
    if "Opponent" not in df.columns and "Opp" not in df.columns:
        # No opponent info; return unchanged but mark neutral if meta not present
        if "defense_meta" not in df.columns:
            df = df.copy(); df["defense_meta"] = "neutral"
        return df

    opp_col = "Opponent" if "Opponent" in df.columns else "Opp"
    dfx = _load_week_csv(week)
    if dfx.empty:
        # No defense file found; mark neutral
        out = df.copy()
        if "defense_meta" not in out.columns:
            out["defense_meta"] = "neutral"
        return out

    # Normalize keys to uppercase strings
    dfx = dfx.copy()
    dfx["Team"] = dfx["Team"].astype(str).str.upper()
    out = df.copy()
    out[opp_col] = out[opp_col].astype(str).str.upper()

    m = out.merge(
        dfx.drop_duplicates("Team"),
        left_on=opp_col,
        right_on="Team",
        how="left",
        suffixes=("", "_def"),
        indicator=True,
    )

    # Row-level meta based on merge success
    m["defense_meta"] = m["_merge"].map({"both": "from_csv", "left_only": "neutral", "right_only": "from_csv"})

    # Drop duplicate key column from defense frame to avoid confusion
    if "Team_def" in m.columns:
        m = m.drop(columns=["Team_def"])

    # Fill sensible defaults (neutral matchup = 1.0 multiplier / 0.0 rates)
    for col, default in [
        ("dvoa_vs_qb", 1.0), ("dvoa_vs_rb", 1.0), ("dvoa_vs_wr", 1.0), ("dvoa_vs_te", 1.0),
        ("explosive_allowed", 0.0), ("sack_rate", 0.0), ("rz_td_allowed", 0.0),
    ]:
        if col not in m.columns:
            m[col] = default
        m[col] = m[col].fillna(default)

    return m.drop(columns=["_merge"])
