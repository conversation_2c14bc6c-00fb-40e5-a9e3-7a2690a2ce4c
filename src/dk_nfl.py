from __future__ import annotations

import os
from typing import Dict, <PERSON><PERSON>

import pandas as pd


# Columns we expect in the DK FantasyCruncher template (allowing common aliases)
_REQUIRED_MIN = {
    "Name": ["Name", "Player"],
    "Position": ["Position", "Roster Position", "Pos", "pos"],
    "Team": ["Team", "TeamAbbrev", "team"],
    "Opponent": ["Opponent", "Opp", "opp"],
    "Salary": ["Salary", "salary"],
    "Game Info": ["Game Info", "GameInfo"],
    "AvgPtsPerGame": ["AvgPtsPerGame", "AvgPointsPerGame"],
    "ID": ["ID", "player_id", "id"],
}


def _find_column(df: pd.DataFrame, options: list[str]) -> Tuple[str | None, pd.Series | None]:
    for opt in options:
        if opt in df.columns:
            return opt, df[opt]
    return None, None


def _simple_team_norm(x: str | None) -> str | None:
    if x is None:
        return None
    return str(x).strip().upper()


def _simple_name_norm(x: str | None) -> str | None:
    if x is None:
        return None
    return " ".join(str(x).split()).strip()


# Best-effort import of project normalizers; fall back to simple rules
try:
    from app.utils.normalize import normalize_team as _proj_norm_team  # type: ignore
    from app.utils.normalize import normalize_name as _proj_norm_name  # type: ignore
except Exception:
    _proj_norm_team = _simple_team_norm  # type: ignore
    _proj_norm_name = _simple_name_norm  # type: ignore


def read_dk_nfl_csv(path: str) -> pd.DataFrame:
    """Read DraftKings NFL FantasyCruncher CSV and normalize key fields.

    - Preserves all original columns exactly as-is
    - Adds helper columns: name_norm, team_norm (not required by FC)
    - If ID is present, preserves it under column "ID"; otherwise leaves it absent
    """
    if not os.path.exists(path):
        raise FileNotFoundError(f"DK NFL CSV not found at {path}")

    # Some vendor exports contain a junk first line of commas. Detect header row.
    header_row = 0
    with open(path, "r", encoding="utf-8") as fh:
        for i, line in enumerate(fh):
            if ("Player" in line or "Name" in line) and ("Pos" in line or "Position" in line):
                header_row = i
                break

    df = pd.read_csv(path, header=header_row)

    # Validate presence of minimal identity columns without renaming the originals
    has_name = any(c in df.columns for c in ["Name", "Player"])
    has_pos = any(c in df.columns for c in ["Position", "Roster Position", "Pos", "pos"])
    has_team = any(c in df.columns for c in ["Team", "TeamAbbrev", "team"])
    has_salary = any(c in df.columns for c in ["Salary", "salary"])
    if not all([has_name, has_pos, has_team, has_salary]):
        raise KeyError("DK NFL CSV missing one of required columns: Name/Player, Position/Pos, Team/TeamAbbrev, Salary")

    # Normalize helpers (kept as extra columns; we will not remove or rename originals)
    if "Name" in df.columns:
        df["name_norm"] = df["Name"].map(_proj_norm_name)
    elif "Player" in df.columns:
        df["name_norm"] = df["Player"].map(_proj_norm_name)

    team_source = "Team" if "Team" in df.columns else ("TeamAbbrev" if "TeamAbbrev" in df.columns else None)
    if team_source:
        df["team_norm"] = df[team_source].map(_proj_norm_team)

    # Do not require ID strictly; preserve if present
    return df


def discover_week_csv(week: int) -> str:
    """Return the expected path for the given week, with cache fallback."""
    primary = f"data/draftkings_NFL_2025-week-{week}_players.csv"
    fallback = f"data/cache/draftkings_NFL_2025-week-{week}_players.csv"
    return primary if os.path.exists(primary) else fallback

