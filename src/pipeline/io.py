from __future__ import annotations

import glob
import os
from typing import Iterable, List, Optional

import pandas as pd


DATA_ROOT = os.path.abspath(os.path.join(os.getcwd(), "data"))
CACHE_DIR = os.path.join(DATA_ROOT, "cache")
os.makedirs(CACHE_DIR, exist_ok=True)


def load_cached(path_pattern: str, required_cols: Optional[Iterable[str]] = None) -> pd.DataFrame:
    """Load cached CSV/Parquet matching a glob pattern.

    Example: load_cached("/data/cache/salaries_*.parquet", required_cols=[...])
    Raises a clear error if no files match or required columns are missing.
    """
    # Support absolute pattern or relative to repo root
    pattern = (
        path_pattern
        if os.path.isabs(path_pattern)
        else os.path.abspath(os.path.join(os.getcwd(), path_pattern.lstrip("/")))
    )

    files = sorted(glob.glob(pattern))
    if not files:
        raise FileNotFoundError(f"No files match pattern: {path_pattern}")

    frames: List[pd.DataFrame] = []
    for fp in files:
        ext = os.path.splitext(fp)[1].lower()
        if ext in (".parquet", ".pq"):
            try:
                df = pd.read_parquet(fp)
            except Exception as e:
                raise RuntimeError(f"Failed to read parquet: {fp}: {e}")
        elif ext in (".csv", ".txt"):
            df = pd.read_csv(fp)
        else:
            raise ValueError(f"Unsupported file extension for {fp}")
        frames.append(df)

    df_all = pd.concat(frames, ignore_index=True)

    if required_cols is not None:
        missing = [c for c in required_cols if c not in df_all.columns]
        if missing:
            raise KeyError(f"Missing required columns {missing} in loaded data from {path_pattern}")

    return df_all


essential_save_engine_checked = False


def _ensure_parquet_engine():
    global essential_save_engine_checked
    if essential_save_engine_checked:
        return
    try:
        import pyarrow  # noqa: F401
    except Exception:
        try:
            import fastparquet  # noqa: F401
        except Exception as e:
            # We avoid raising here to keep unit tests independent of parquet engines
            # The save will fall back to CSV if parquet engine is not available
            pass
    essential_save_engine_checked = True


def save_table(df: pd.DataFrame, name: str) -> str:
    """Save a DataFrame to /data/cache/{name}.parquet (or CSV fallback).

    Returns the file path used.
    """
    _ensure_parquet_engine()
    out_parquet = os.path.join(CACHE_DIR, f"{name}.parquet")
    try:
        df.to_parquet(out_parquet, index=False)
        return out_parquet
    except Exception:
        # Fallback to CSV to avoid hard dependency in environments without parquet engines
        out_csv = os.path.join(CACHE_DIR, f"{name}.csv")
        df.to_csv(out_csv, index=False)
        return out_csv

