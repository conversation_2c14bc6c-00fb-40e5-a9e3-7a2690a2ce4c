from __future__ import annotations

import os
from typing import List

import pandas as pd

from .io import load_cached


def _normalize_team(team: str) -> str:
    if not isinstance(team, str):
        return team
    return team.strip().upper()


def _drop_out_players(df: pd.DataFrame) -> pd.DataFrame:
    if "status" not in df.columns:
        return df
    mask = ~df["status"].astype(str).str.upper().isin(["OUT", "IR", "PUP"])
    return df.loc[mask].copy()


def build_feature_table(slate_date: str, league: str = "NFL") -> pd.DataFrame:
    """Assemble the feature table for the slate from cached sources.

    This expects data to be pre-cached under /data or /data/cache with reasonable
    filenames. It performs ID normalization, OUT-status filtering, team/opp
    normalization, and computes derived features required by projections.

    Returns a wide dataframe with at least these columns:
    - player_id, slate_id, team, opp, pos
    - team_plays, pass_rate, implied_total, spread
    - usage shares: target_share, rush_share, aDOT, rz_tgt_share, rz_rush_share
    - efficiency: yards_per_att, yards_per_tgt, yards_per_carry, pass_td_rate, rec_td_rate, rush_td_rate
    - snap_share
    """
    # The specifics of file names/formats may vary project-to-project.
    # Provide flexible patterns with reasonable fallbacks.

    # Salaries
    try:
        salaries = load_cached(f"/data/cache/dk_salaries_{slate_date}_*.parquet")
    except Exception:
        try:
            salaries = load_cached(f"/data/dk_salaries_{slate_date}_*.csv")
        except Exception:
            # Minimal stub when no data is present; empty frame with expected cols
            salaries = pd.DataFrame(columns=["player_id", "slate_id", "team", "opp", "pos", "salary"])  

    # Depth/roles/snap
    try:
        roles = load_cached(f"/data/cache/roles_{slate_date}_*.parquet")
    except Exception:
        roles = pd.DataFrame(columns=["player_id", "snap_share", "rush_share", "target_share", "aDOT", "status"])  

    # Team totals / spreads
    try:
        totals = load_cached(f"/data/cache/team_lines_{slate_date}_*.parquet")
    except Exception:
        totals = pd.DataFrame(columns=["team", "opp", "implied_total", "spread"])  

    # Pace / plays
    try:
        pace = load_cached(f"/data/cache/pace_{slate_date}_*.parquet")
    except Exception:
        pace = pd.DataFrame(columns=["team", "neutral_pace", "team_plays", "pass_rate"])  

    # Defensive metrics
    try:
        defense = load_cached(f"/data/cache/defense_{slate_date}_*.parquet")
    except Exception:
        defense = pd.DataFrame(columns=["team", "def_epa_per_play", "opp_off_epa_per_play"])  

    # Weather (optional)
    try:
        weather = load_cached(f"/data/cache/weather_{slate_date}_*.parquet")
    except Exception:
        weather = pd.DataFrame(columns=["team", "is_roof", "is_rain", "is_wind", "temp_f"])  

    # Injury/news latest status (optional, might be inside roles already)
    try:
        injuries = load_cached(f"/data/cache/injuries_{slate_date}_*.parquet")
    except Exception:
        injuries = pd.DataFrame(columns=["player_id", "status"])  

    # Normalize IDs/teams
    for df in (salaries, roles, totals, pace, defense, weather):
        if "team" in df.columns:
            df["team"] = df["team"].map(_normalize_team)
        if "opp" in df.columns:
            df["opp"] = df["opp"].map(_normalize_team)

    # Merge
    df = salaries.copy()
    # Prefer player_id as key; some feeds might use id
    if "id" in df.columns and "player_id" not in df.columns:
        df = df.rename(columns={"id": "player_id"})

    # Left-join roles/usage
    if not roles.empty:
        df = df.merge(roles.drop_duplicates("player_id"), on="player_id", how="left")

    # Join team-level data
    for tdf in (totals, pace, defense, weather):
        if tdf.empty:
            continue
        suffix = ""
        df = df.merge(tdf.drop_duplicates("team"), on="team", how="left", suffixes=("", suffix))

    # Injury overrides
    if not injuries.empty:
        df = df.merge(injuries.drop_duplicates("player_id"), on="player_id", how="left", suffixes=("", "_inj"))
        # prefer explicit injury status if present
        df["status"] = df["status_inj"].combine_first(df["status"]) if "status_inj" in df.columns else df.get("status")

    df = _drop_out_players(df)

    # Derived features
    if "pass_rate" not in df.columns or df["pass_rate"].isna().all():
        df["pass_rate"] = 0.56
    df["pass_rate"] = df["pass_rate"].clip(0.2, 0.85)

    if "team_plays" not in df.columns or df["team_plays"].isna().all():
        # derive from pace when available
        if "neutral_pace" in df.columns and df["neutral_pace"].notna().any():
            # rough mapping pace->plays
            df["team_plays"] = (df["neutral_pace"].fillna(df["neutral_pace"].median()) * 60.0).clip(50, 75)
        else:
            df["team_plays"] = 60.0

    if "rush_rate" not in df.columns:
        df["rush_rate"] = 1.0 - df["pass_rate"]

    # Reasonable defaults for implied total and spread
    if "implied_total" not in df.columns:
        df["implied_total"] = 21.0
    df["implied_total"] = df["implied_total"].fillna(21.0)

    if "spread" not in df.columns:
        df["spread"] = 0.0
    df["spread"] = df["spread"].fillna(0.0)

    # Usage defaults
    for c, default in [
        ("target_share", 0.18),
        ("rush_share", 0.30),
        ("aDOT", 8.0),
        ("rz_tgt_share", 0.05),
        ("rz_rush_share", 0.10),
        ("snap_share", 0.7),
    ]:
        if c not in df.columns:
            df[c] = default
        df[c] = df[c].fillna(default)

    # Efficiency defaults
    for c, default in [
        ("yards_per_att", 7.2),
        ("yards_per_tgt", 7.5),
        ("yards_per_carry", 4.4),
        ("pass_td_rate", 0.045),
        ("rec_td_rate", 0.040),
        ("rush_td_rate", 0.030),
    ]:
        if c not in df.columns:
            df[c] = default
        df[c] = df[c].fillna(default)

    # Defensive mapping for opponent allowed (optional)
    if "opp_off_epa_per_play" not in df.columns:
        df["opp_off_epa_per_play"] = -0.02

    # Ensure minimal identity columns
    for c in ["player_id", "slate_id", "team", "opp", "pos"]:
        if c not in df.columns:
            df[c] = None

    return df

